# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.js
.wakatime-project
# testing
/coverage

# next.js
/.next/
/out/

# production
/build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# env
.env*.local
.env
.env.development

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts

.history/

# Service Worker
/public/service-worker.js
# Sentry Config File
.env.sentry-build-plugin

/public/tinymce

.yarn
import { ReactNode } from "react";
import { create } from "zustand";
import { ButtonOwnProps } from "@mui/material";
import { ButtonProps } from "@/components/ui/Button/button";

type TModalProps = {
  className?: string;
  containerClassName?: string;
  showCloseIcon?: boolean;
};

type TAction = {
  label: ReactNode;
  onClick?: () => void;
  className?: string;
} & ButtonProps;

export type TOpenModalProps = {
  modalProps?: TModalProps;
  title?: ReactNode;
  subTitle?: ReactNode;
  body?: ReactNode | (() => ReactNode);
  actions?: TAction[];
  icon?: ReactNode;
  width?: number;
  closable?: boolean;
  onClose?: () => void;
};

interface IUseModalStore extends TOpenModalProps {
  isOpen: boolean;
  modalProps?: TModalProps;
  openModal: (props: TOpenModalProps) => void;
  closeModal: () => void;
}

const useModalStore = create<IUseModalStore>(set => ({
  isOpen: false,
  modalProps: {},
  title: undefined,
  subTitle: undefined,
  body: undefined,
  actions: undefined,
  icon: undefined,
  width: 300,
  onClose: undefined,
  closable: true,
  openModal: props =>
    set({
      isOpen: true,
      modalProps: props?.modalProps || {},
      title: props?.title || undefined,
      subTitle: props?.subTitle || undefined,
      body: props?.body || undefined,
      actions: props?.actions || undefined,
      icon: props?.icon || undefined,
      width: props?.width || undefined,
      onClose: props?.onClose || undefined,
      closable: props?.closable !== undefined ? props?.closable : true
    }),
  closeModal: () => {
    useModalStore?.getState()?.onClose?.();

    set({
      isOpen: false,
      modalProps: {},
      title: undefined,
      subTitle: undefined,
      body: undefined,
      actions: undefined,
      icon: undefined,
      onClose: undefined,
      closable: true
    });
  }
}));

export default useModalStore;

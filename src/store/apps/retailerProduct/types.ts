import { snakeCaseToCamelCase } from "@/utils/typescript/snakeCaseToCamelCase";
import { Authenticity, Condition } from "../product/types";

export type TRetailerImportListData = snakeCaseToCamelCase<TRetailerImportPayloadResponse>;
export type TRetailerProductDetail = snakeCaseToCamelCase<TRetailerProductPayloadResponse>;
export type TAddRetailerImportListData = snakeCaseToCamelCase<TPostRetailerImportPayloadResponse>;

export type TRetailerCategoryData = snakeCaseToCamelCase<TRetailerCategoryPayloadResponse>;

export interface DeleteRetailerProduct {
  ids: string[];
}

export interface DeleteRetailerProductResponse {
  data: {};
  status: string;
}

export interface TPostRetailerImportPayloadResponse {
  data: Datum[];
  status: string;
}

export interface TRetailerCategoryPayloadData {
  hierarchy: string;
  id: string;
  name: string;
  storeId: string;
}

export interface TRetailerCategoryPayloadResponse {
  data: TRetailerCategoryPayloadData[];
  status: string;
}

export type TImportListFilter = {
  page?: number;
  page_size?: number;
  filters?: {
    id?: string;
    title?: string;
    draft?: boolean;
  };
  sorts?: {
    [key: string]: "asc" | "desc";
  };
};

export interface TRetailerProductPayloadResponse {
  data: Datum;
  status: string;
}

export interface TRetailerImportPayloadResponse {
  data: Datum[];
  status: string;
  pagination: Pagination;
}

interface Pagination {
  page: number;
  page_size: number;
  total: number;
}

interface Datum {
  category: Category;
  category_id: string;
  created_at: string;
  delete_at: string;
  description: string;
  id: string;
  images: Image[];
  is_active: boolean;
  is_live: boolean;
  origin_product: Originproduct;
  product_id: string;
  retailer_id: string;
  store_id: string;
  tags: string[];
  title: string;
  updated_at: string;
  variants: TRetailerImportPayloadVariant[];
}

export interface TRetailerImportPayloadVariant {
  compare_at_price: number;
  id: string;
  is_active: boolean;
  is_live: boolean;
  live_store_id?: string;
  sales_price: number;
}

interface Originproduct {
  category_id: string;
  cheapest_price: number;
  cheapest_variant: Cheapestvariant;
  cover: Image;
  description: string;
  has_variant: boolean;
  id: string;
  images: Image[];
  premium: boolean;
  properties: Properties;
  supplier: Supplier;
  supplier_id: string;
  title: string;
  variants: TRetailerPeoductVariant[];
  winning: boolean;
}

export interface TRetailerPeoductVariant {
  id: string;
  inventory: number;
  map?: number;
  is_active: boolean;
  authenticity: Authenticity;
  backorder: boolean;
  commission: number;
  condition: Condition;
  options: Options;
  retail_price: number;
  sku: string;
  minimum_retail_price?: string;
  cost?: string;
}

interface Supplier {
  branded_invoicing_allowed: boolean;
  cover: string;
  created_at: string;
  description: string;
  id: string;
  logo: string;
  name: string;
  processing_time: Processingtime;
  return_policy: Returnpolicy;
  shipping_policies: Shippingpolicy[];
  website: string;
}

interface Shippingpolicy {
  description: string;
  excluded: boolean;
  extra_item_rate: number;
  id: string;
  prepaid: boolean;
  rate: number;
  rate_type: string;
  shipping_carrier: string;
  shipping_time: Processingtime;
  shipping_to: string;
}

interface Returnpolicy {
  address: Address;
  description: string;
  is_allowed: boolean;
  shipping_payer: string;
  window_time: Processingtime;
}

interface Address {
  address1: string;
  address2: string;
  city: string;
  company: string;
  location_id: string;
  phone_number: string;
  state: string;
  zip: string;
}

interface Processingtime {
  max: number;
  min: number;
}

interface Properties {
  additionalProp1: string[];
  additionalProp2: string[];
  additionalProp3: string[];
}

interface Cheapestvariant {
  created_at: string;
  id: string;
  inventory: number;
  map?: number;
  is_active: boolean;
  authenticity: Authenticity;
  backorder: boolean;
  commission: number;
  condition: Condition;
  options: Options;
  product_id: string;
  retail_price: number;
  sku: string;
  status: string;
  updated_at: string;
}

interface Options {
  additionalProp1: string;
  additionalProp2: string;
  additionalProp3: string;
}

interface Image {
  alt: string;
  marked_as_cover: boolean;
  url: string;
}

interface Category {
  created_at: string;
  icon: string;
  id: string;
  image: string;
  is_active: boolean;
  name: string;
  parent_id: string;
  position: number;
  slug: string;
  sub_categories: string[];
  updated_at: string;
}

export interface PutRetailerProductImportBody {
  category_id: string;
  description: string;
  images: PutRetailerProductImportImage[];
  is_active: boolean;
  tags: string[];
  title: string;
  variants: Variant[];
}

interface Variant {
  compare_at_price: number;
  id: string;
  is_active: boolean;
  sales_price: number;
}

export interface PutRetailerProductImportImage {
  alt: string;
  marked_as_cover: boolean;
  url: string;
}

export interface PostRetailerProductPush {
  ids: string[];
}

export interface PostRetailerProductUnPush {
  ids: string[];
}

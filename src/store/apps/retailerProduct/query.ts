import { rtkBaseQuery } from "@/utils/services";
import { createApi } from "@reduxjs/toolkit/query/react";

import { profileApiRoutes } from "@/constants/apiRoutes/apiServiceRoutes";
import { RETAILER_CATEGORY, R<PERSON><PERSON><PERSON>ER_PRODUCT, R<PERSON><PERSON><PERSON><PERSON>_PRODUCT_LIST } from "@/constants/queryKeys";
import { convertCamelToSnake } from "@/utils/helpers/objectHelper";
import { snakeCaseToCamelCase } from "@/utils/typescript/snakeCaseToCamelCase";
import {
  DeleteRetailerProduct,
  DeleteRetailerProductResponse,
  PostRetailerProductPush,
  PostRetailerProductUnPush,
  PutRetailerProductImportBody,
  TAddRetailerImportListData,
  TRetailerCategoryData,
  TRetailerImportListData,
  TRetailerProductDetail
} from "./types";

export const RetailerProduct = createApi({
  reducerPath: "RetailerProduct",
  tagTypes: [RETAILER_PRODUCT_LIST, R<PERSON><PERSON><PERSON><PERSON>_PRODUCT, RETA<PERSON>ER_CATEGORY],
  baseQuery: rtkBaseQuery,
  keepUnusedDataFor: 0,
  endpoints: builder => ({
    /* ---------------------------------- list ---------------------------------- */
    getRetailerImportsList: builder.query<TRetailerImportListData, string>({
      query: params => ({
        url: `${profileApiRoutes.retailerProduct}?${params}`
      }),
      providesTags: [RETAILER_PRODUCT_LIST]
    }),
    postRetailderAddToImportList: builder.mutation<TAddRetailerImportListData, { productId: string | number }>({
      query: ({ productId }) => {
        return {
          url: profileApiRoutes.retailerProduct,
          method: "post",
          data: { product_id: productId }
        };
      },
      invalidatesTags: [RETAILER_PRODUCT_LIST]
    }),
    deleteRetailderProduct: builder.mutation<DeleteRetailerProductResponse, { body: DeleteRetailerProduct }>({
      query: ({ body }) => {
        return {
          url: profileApiRoutes.retailerProduct,
          method: "delete",
          data: convertCamelToSnake(body)
        };
      },
      invalidatesTags: [RETAILER_PRODUCT_LIST]
    }),
    /* --------------------------- single product {id} -------------------------- */
    getRetailerProduct: builder.query<TRetailerProductDetail, string>({
      query: id => ({
        url: `${profileApiRoutes.retailerProductDetail({ id })}`
      }),
      providesTags: [RETAILER_PRODUCT]
    }),
    putRetailerProduct: builder.mutation<
      TAddRetailerImportListData,
      { productId: string; body: snakeCaseToCamelCase<PutRetailerProductImportBody> }
    >({
      query: ({ productId, body }) => {
        return {
          url: profileApiRoutes.retailerProductDetail({ id: productId }),
          method: "put",
          data: convertCamelToSnake(body)
        };
      },
      invalidatesTags: [RETAILER_PRODUCT]
    }),
    deleteRetailerProduct: builder.mutation<TAddRetailerImportListData, { productId: string }>({
      query: ({ productId }) => {
        return {
          url: profileApiRoutes.retailerProductDetail({ id: productId }),
          method: "delete"
        };
      },
      invalidatesTags: [RETAILER_PRODUCT_LIST, RETAILER_PRODUCT]
    }),

    /* ------------------------------ product/push ------------------------------ */
    postRetailderProductPush: builder.mutation<TAddRetailerImportListData, { body: PostRetailerProductPush }>({
      query: ({ body }) => {
        return {
          url: profileApiRoutes.retailerProductPush,
          method: "post",
          data: convertCamelToSnake(body)
        };
      },
      invalidatesTags: [RETAILER_PRODUCT_LIST]
    }),
    postRetailderProductUnPush: builder.mutation<TAddRetailerImportListData, { body: PostRetailerProductUnPush }>({
      query: ({ body }) => {
        return {
          url: profileApiRoutes.retailerProductUnPush,
          method: "post",
          data: convertCamelToSnake(body)
        };
      },
      invalidatesTags: [RETAILER_PRODUCT_LIST]
    }),
    getRetailerCategoryList: builder.query<TRetailerCategoryData, void>({
      query: () => ({
        url: `${profileApiRoutes.retailerCategory}`
      }),
      providesTags: [RETAILER_CATEGORY]
    }),
    PutRetailerCategory: builder.mutation<any, { id?: string; name: string }>({
      query: ({ name, id }) => ({
        url: `${profileApiRoutes.retailerCategory}`,
        method: "put",
        data: { name, id }
      }),
      invalidatesTags: [RETAILER_CATEGORY]
    })
  })
});

// Export hooks for usage in functional components, which are
// auto-generated based on the defined endpoints
export const {
  useGetRetailerImportsListQuery,
  usePostRetailderAddToImportListMutation,
  usePutRetailerProductMutation,
  usePostRetailderProductPushMutation,
  usePostRetailderProductUnPushMutation,
  useGetRetailerProductQuery,
  useDeleteRetailerProductMutation,
  useDeleteRetailderProductMutation,
  useGetRetailerCategoryListQuery,
  usePutRetailerCategoryMutation
} = RetailerProduct;

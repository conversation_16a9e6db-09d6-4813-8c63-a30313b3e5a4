import { Icon } from "@iconify/react";
import SelectWebsite from "./SelectWebsite";
import useModal from "@/utils/hooks/useModal";
import { useTranslation } from "react-i18next";

function AddStore({ onClick }: { onClick?: VoidFunction }) {
  const { showModal } = useModal();
  const { t } = useTranslation();

  const handleSelectWebsite = () => {
    onClick?.();
    showModal({
      body: () => <SelectWebsite />,
      modalProps: {
        showCloseIcon: false
      }
    });
  };

  return (
    <div className="flex items-center gap-2 cursor-pointer" onClick={handleSelectWebsite}>
      <div className="flex justify-center items-center w-9 h-9 rounded-md bg-purple-50">
        <Icon icon="ph:plus" className="text-purple-500" width={16} height={16} />
      </div>

      <span className="text-body4-medium text-gray-999 whitespace-nowrap">{t("accountInfoItem.addStore")}</span>
    </div>
  );
}

export default AddStore;

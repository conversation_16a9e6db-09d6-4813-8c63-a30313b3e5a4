import { useGetRetailerStoreQuery } from "@/store/apps/retailer";
import { useRetailerStore } from "@/store/zustand/retailerStore/RetailerStore";
import { useSelector } from "@/store/hooks";
import useModal from "@/utils/hooks/useModal";
import { Icon } from "@iconify/react";
import { Avatar, Button, CircularProgress, Theme, useMediaQuery } from "@mui/material";
import Image from "next/image";
import { useTranslation } from "react-i18next";
import SelectWebsite from "./SelectWebsite";
import SelectStore from "./SelectStore";
import useRoleBasePath from "@/utils/hooks/useRoleBasePath";
import { routes } from "@/constants/routes";
import Link from "next/link";
import useClipboard from "@/utils/hooks/useClipboard";
import { twMerge } from "tailwind-merge";
import { ensureUrlScheme } from "@/utils/helpers";
import DisconnectStoreButton from "./DisconnectStore";
import ConfigureStoreButton from "./ConfigureStoreButton";
import { TRetailerProfileData } from "@/store/apps/retailer/types";
import StoreModalBody from "./StoreWarningModalBody";

export default function StoreInfo() {
  const { t } = useTranslation();
  const { showModal, hideModal } = useModal();
  const makePath = useRoleBasePath();

  const { selectedRetailerStoreId, data: retailerStore, isLoading } = useRetailerStore();
  const { copyToClipboard, isCopied } = useClipboard();
  // const { data: storeData, isError, isLoading } = useGetRetailerStoresQuery();
  const isMobile = useMediaQuery((theme: Theme) => theme.breakpoints.down(768));

  const retailerProfile = useSelector((state: any) => state?.Retailer?.queries["getRetailerProfile(undefined)"] || {});
  const retailerProfileData = retailerProfile?.data?.data as TRetailerProfileData;
  const activeProfile = retailerProfileData?.status === "Active";

  const handleNotActiveUser = () => {
    showModal({
      body: (
        <StoreModalBody
          title={t("userNotActive.title")}
          subtitle={t("userNotActive.subtitle")}
          buttonText={t("understand")}
        />
      ),
      width: isMobile ? undefined : 428
    });
  };

  const { data: retailerStoreSingle } = useGetRetailerStoreQuery(
    {
      id: selectedRetailerStoreId || ""
    },
    { skip: !selectedRetailerStoreId }
  );

  const websiteLink = ensureUrlScheme(retailerStore?.url ?? "");

  const handleSelectStore = () => {
    if (!activeProfile) {
      return handleNotActiveUser();
    } else
      showModal({
        body: <SelectStore />
      });
  };

  const handleSelectWebsite = () => {
    if (!activeProfile) {
      return handleNotActiveUser();
    } else
      showModal({
        body: () => <SelectWebsite />,
        modalProps: {
          showCloseIcon: false
        },
        width: isMobile ? undefined : 450
      });
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center">
        <CircularProgress />
      </div>
    );
  }

  if (!retailerStore?.id) {
    return (
      <div>
        <span className="text-gray-600 text-caption-medium">{t("store.selectStore")}</span>

        <div className="flex flex-col items-center gap-1 mt-3">
          <Image src="/images/svgs/noStore.svg" width={180} height={149} alt="emptyStore" />
          <span className="text-body4-medium text-gray-999">{t("store.emptyStore")}</span>
          <Button
            onClick={handleSelectWebsite}
            variant="contained"
            className="mt-4 bg-purple-50 text-gray-999 text-body4-medium rounded-md py-2 px-4 hover:text-[#ffff] group"
            startIcon={<Icon icon="ph:plus" className={twMerge("group-hover:text-[#ffff] text-purple-500")} />}
          >
            {t("store.addNewStore")}
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div>
      <p className="text-gray-600 text-caption-medium xmd:block hidden ">{t("store.selectedStore")}</p>

      <div className="xmd:border xmd:border-solid xmd:border-gray-50  xmd:p-4 p-0 mt-2 rounded-lg">
        <p className="text-gray-400 text-caption-regular xmd:hidden block mb-2">{t("store.selectedStore")}</p>
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            {!!retailerStore?.logo && <Avatar src={retailerStore?.logo} className="size-10 rounded-md" />}

            <span className="text-body3-medium text-gray-999">{retailerStore?.name}</span>
          </div>

          <Button
            variant="contained"
            onClick={handleSelectStore}
            className=" bg-purple-50 text-purple-500 text-body4-medium rounded-md py-3 max-h-10 px-4 hover:text-[#ffff] group"
            endIcon={
              <Icon icon="solar:round-alt-arrow-down-outline" className="text-purple-500 group-hover:text-[#ffff]" />
            }
          >
            {t("store.changeStore")}
          </Button>
        </div>

        <div className="flex flex-col gap-2 mt-4">
          {!!retailerStore?.url && (
            <div className="flex items-center justify-between">
              <span className="text-gray-600 text-body4-medium">{t("store.websiteLink")}</span>
              <div className="flex items-center gap-1">
                <Link
                  dir="ltr"
                  className="text-gray-999 text-body4-medium truncate w-32 "
                  href={websiteLink}
                  target="_blank"
                >
                  {retailerStore?.url}
                </Link>
                <div className="size-4 flex items-center justify-center">
                  <Icon
                    icon={isCopied ? "flat-color-icons:checkmark" : "icon-park-outline:copy"}
                    className={twMerge(isCopied ? "size-4 mb-1" : "size-3 ", "text-gray-200 cursor-pointer")}
                    onClick={() => copyToClipboard(retailerStore?.url)}
                  />
                </div>
              </div>
            </div>
          )}

          <ConfigureStoreButton
            disabled={!activeProfile}
            handleNotActiveUser={handleNotActiveUser}
            href={makePath(
              `${routes.profile}?settingType=configureStore&integrationId=${retailerStore?.integration?.platform?.key}&id=${retailerStore?.id}&type=form&edit={true}`
            )}
          />

          <DisconnectStoreButton
            disabled={!activeProfile}
            handleNotActiveUser={handleNotActiveUser}
            config={retailerStoreSingle?.data?.integration?.config}
            integrationId={retailerStore?.integration?.platform?.key}
            selectedRetailerStoreId={selectedRetailerStoreId}
            retailerId={retailerStore?.retailerId ?? ""}
            defaultIsActive={!!retailerStore?.isActive}
          />
        </div>
      </div>

      {/* <div className="mt-4">
        <AddStore />
      </div> */}
    </div>
  );
}

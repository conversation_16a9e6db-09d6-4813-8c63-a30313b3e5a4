import { TRetailerImportListData } from "@/store/apps/retailerProduct/types";
import { ReactNode } from "react";

export type ProductsMobileProps = {
  isDraft: boolean;
  hasNextPage: boolean;
  isLoading: boolean;
  fetchNextPage?: () => Promise<unknown>;
  data?: TRetailerImportListData["data"] | undefined;
  productData?: TRetailerImportListData | undefined;
  renderer?: (d: { data: unknown }) => ReactNode;
  page: number;
  setFilters: any;
  filters: any;
  checkedIds: string[];
  setCheckedIds: React.Dispatch<React.SetStateAction<string[]>>;
  onClickDelete: (id: string[]) => void;
  onClickPublish: (id: string[]) => void;
  handleCheckItem: (id: string) => void;
  handleFormSubmit: (itemId: any) => Promise<void>;
  onClickThreeDots: (item: TRetailerImportListData["data"][number]) => void;
  setFormRef: (itemId: any, ref: any) => void;
  totalCount: number;
  onClickUnPublish: (id: string[]) => void;
};
export type ProductsDesktopProps = ProductsMobileProps;

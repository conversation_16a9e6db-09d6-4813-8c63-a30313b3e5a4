"use client";

import React, { useCallback, useEffect, useMemo, useRef, useState } from "react";
import { Divider, Theme, useMediaQuery } from "@mui/material";
import { routes } from "@/constants/routes";
import { useTranslation } from "react-i18next";
import { CircularProgress } from "@mui/material";
import useRoleBasePath from "@/utils/hooks/useRoleBasePath";
import Link from "next/link";
import useModal from "@/utils/hooks/useModal";
import {
  useDeleteRetailderProductMutation,
  useGetRetailerImportsListQuery,
  usePostRetailderProductPushMutation,
  usePostRetailderProductUnPushMutation
} from "@/store/apps/retailerProduct";
import { clientDefaultErrorHandler } from "@/utils/services/utils";
import { generateBackendFilters, generateBackendSorts } from "@/utils/services/transformers";
import { useFiltersState } from "./Filters/useFiltersState";
import { calculateTotalCount, omitEmptyValues } from "@/utils/helpers";
import { useConversationStart } from "@/utils/hooks/useConversationStart";
import { TRetailerImportListData } from "@/store/apps/retailerProduct/types";
import ProductsMobile from "./ProductsMobile";
import ProductsDesktop from "./ProductsDesktop";

function Products({ isDraft = false }: { isDraft?: boolean }) {
  /* ---------------------------------- hooks --------------------------------- */
  const { t } = useTranslation();
  const makePath = useRoleBasePath();
  const { showModal, hideModal } = useModal();
  const isMobile = useMediaQuery((theme: Theme) => theme.breakpoints.down(768));
  const { onChatStart, isConversationLoading } = useConversationStart();

  /* --------------------------------- states --------------------------------- */
  const { filters, setFilters } = useFiltersState();
  const { page, pageSize, created_at, updated_at, ...restFilters } = filters || {};
  const nonEmptyFilters = useMemo(() => omitEmptyValues(restFilters), [restFilters]);

  const [checkedIds, setCheckedIds] = useState<string[]>([]);

  const editFormRefs = useRef(new Map());

  // const handleSaveClick = () => {
  //   // You can programmatically submit the form from the parent
  //   console.log("editFormRef1", editFormRef);
  //   console.log("editFormRef2", { editFormRef });
  //   console.log("editFormRef3", editFormRef.current);

  //   if (editFormRef.current) {
  //     editFormRef.current.submitForm();
  //   }
  // };

  const setFormRef = useCallback((itemId: any, ref: any) => {
    if (ref) {
      editFormRefs.current.set(itemId, ref);
    } else {
      editFormRefs.current.delete(itemId);
    }
  }, []);

  const finalFilters = generateBackendFilters({ ...nonEmptyFilters, draft: isDraft });
  const sorts = generateBackendSorts(
    omitEmptyValues({
      created_at: (created_at as any) || undefined,
      updated_at: (updated_at as any) || undefined
    })
  );

  const queryParts = [
    `page_size=${filters?.pageSize}`,
    `page=${filters?.page}`,
    finalFilters ? `filters=${finalFilters}` : "",
    sorts ? `sorts=${sorts}` : ""
  ].filter(part => part !== "");
  const queryString = queryParts.join("&");

  /* ----------------------------------- rtk ---------------------------------- */
  const {
    data: productData,
    isLoading: isProductLoading,
    isFetching: isProductFetching,
    isError: isProductError
  } = useGetRetailerImportsListQuery(queryString || "", {
    skip: !filters?.page,
    refetchOnMountOrArgChange: true
  });

  useEffect(() => {
    if (isMobile) {
      // This code runs on initial load and page refresh
      setFilters({ page: 1 });
    }
    return () => {
      if (isMobile) {
        setFilters({ page: 1 });
      }
    };
  }, []);

  //   const isProductLoading = true;
  const [pushProducts] = usePostRetailderProductPushMutation();
  const [unpushProducts] = usePostRetailderProductUnPushMutation();
  const [deleteProducts] = useDeleteRetailderProductMutation();
  const totalCount = productData?.pagination?.total ?? 0;

  const hasNextPage = useMemo(() => calculateTotalCount({ pageSize, totalCount }) > page, [pageSize, totalCount, page]);

  const fetchNextPage = () => {
    setFilters({ page: page + 1 });
    return new Promise(() => {});
  };

  /* -------------------------------- functions ------------------------------- */

  const handlePublish = useCallback(
    (ids: string[]) => {
      hideModal();
      setTimeout(() => {
        showModal({
          body: (
            <div className="flex items-center justify-center w-full">
              <CircularProgress />
            </div>
          ),
          closable: false
        });

        pushProducts({ body: { ids: ids } })
          .then((res: any) => {
            if (!res?.error) {
              setCheckedIds([]);
            }
          })
          .catch((err: any) => clientDefaultErrorHandler({ error: err }))
          .finally(() => {
            hideModal();
          });
      }, 0);
    },
    [hideModal, pushProducts, showModal]
  );

  const getFormRef = useCallback((itemId: any) => {
    return editFormRefs.current.get(itemId);
  }, []);

  const onClickPublish = useCallback((id: string[]) => {
    showModal({
      title: t("retailerProduct.publishModalTitle"),
      subTitle: t("retailerProduct.publishModalSubtitle"),
      icon: "/images/svgs/publish-01.svg",
      actions: [
        {
          label: t("retailerProduct.cancel"),
          variant: "secondaryGray",
          onClick: hideModal
        },
        {
          label: t("retailerProduct.yesPublish"),
          variant: "primary",
          onClick: () => handlePublish(id)
        }
      ]
    });
  }, []);

  // Function to handle form submission with proper async/await
  const handleFormSubmit = useCallback(
    async (itemId: any) => {
      const currentFormRef = getFormRef(itemId);

      if (!currentFormRef) {
        console.warn(`Form ref not found for item ${itemId}`);
        return;
      }

      try {
        await currentFormRef.submitForm();

        if (!Object.keys(currentFormRef.errors || {})?.length) {
          setTimeout(() => {
            onClickPublish([itemId]);
          }, 0);
        }
      } catch (error) {
        console.error("Form submission error:", error);
      }
    },
    [getFormRef, onClickPublish]
  );

  const handleUnPublish = (ids: string[]) => {
    hideModal();
    setTimeout(() => {
      showModal({
        body: (
          <div className="flex items-center justify-center w-full">
            <CircularProgress />
          </div>
        ),
        closable: false
      });

      unpushProducts({ body: { ids: ids } })
        .then((res: any) => {
          if (!res?.error) {
            setCheckedIds([]);
          }
        })
        .catch((err: any) => clientDefaultErrorHandler({ error: err }))
        .finally(() => {
          hideModal();
        });
    }, 0);
  };

  const handleDelete = (ids: string[]) => {
    hideModal();
    setTimeout(() => {
      showModal({
        body: (
          <div className="flex items-center justify-center w-full">
            <CircularProgress />
          </div>
        ),
        closable: false
      });

      deleteProducts({ body: { ids: ids } })
        .then((res: any) => {
          if (!res?.error) {
            setCheckedIds([]);
          }
        })
        .catch((err: any) => clientDefaultErrorHandler({ error: err }))
        .finally(() => {
          hideModal();
        });
    }, 0);
  };

  const onClickUnPublish = (id: string[]) => {
    showModal({
      title: t("retailerProduct.unpublishModalTitle"),
      subTitle: t("retailerProduct.unpublishModalSubtitle"),
      icon: "/images/svgs/unpublish-01.svg",
      actions: [
        {
          label: t("retailerProduct.cancel"),
          variant: "secondaryGray",
          onClick: hideModal
        },
        {
          label: t("retailerProduct.unpublish"),
          variant: "destructivePrimary",
          onClick: () => handleUnPublish(id)
        }
      ]
    });
  };

  const onClickDelete = (id: string[]) => {
    showModal({
      title: t("retailerProduct.removePublishModalTitle"),
      subTitle: t("retailerProduct.removePublishModalSubTitle"),
      icon: "/images/svgs/removePublish.svg",
      actions: [
        {
          label: t("retailerProduct.cancel"),
          variant: "secondaryGray",
          onClick: hideModal
        },
        {
          label: t("retailerProduct.yesUnpublish"),
          variant: "destructivePrimary",
          onClick: () => handleDelete(id)
        }
      ]
    });
  };

  const handleCheckItem = (id: string) => {
    const existId = checkedIds?.find(item => item === id);

    if (existId) {
      setCheckedIds(prev => prev?.filter(item => item !== id));
    } else {
      setCheckedIds(prev => [...prev, id]);
    }
  };

  const onClickThreeDots = (item: TRetailerImportListData["data"][number]) => {
    showModal({
      body: (
        <div>
          <>
            <p className="truncate mt-3 w-[90%] text-body4-medium mb-3">{item?.title}</p>
            <p className="pb-3.5 pt-1.5 text-[13px]">{t("retailerProduct.buySample")}</p>
            <Divider className="bg-gray-50 !my-0" />
            <Link href={`${makePath(routes.product)}/${item.originProduct?.id}`} target="_blank">
              <p className="py-3.5 text-[13px]">{t("retailerProduct.productPage")}</p>
            </Link>
            <Divider className="bg-gray-50 !my-0" />
            <p
              className="py-3.5 text-[13px]"
              onClick={() =>
                !isConversationLoading &&
                onChatStart({
                  content: t("chats.product", { name: item?.title }),
                  partnerId: item?.originProduct?.supplierId
                })
              }
            >
              {t("retailerProduct.contactWithSupplier")}
            </p>
            {/* {!isDraft && (
              <>
                <Divider className="bg-gray-50 !my-0" />
                <p
                  className="py-3.5 text-[13px] text-v2-content-on-error-2"
                  onClick={() => onClickUnPublish([item?.id])}
                >
                  {t("retailerProduct.unPublish")}
                </p>
              </>
            )} */}

            <Divider className="bg-gray-50 !my-0" />
            <p
              className="py-3.5 text-[13px] text-v2-content-on-error-2"
              onClick={() => !isConversationLoading && onClickDelete([item?.id])}
            >
              {t("retailerProduct.removeFromStore")}
            </p>
          </>
        </div>
      )
    });
  };
  if (isMobile) {
    return (
      <ProductsMobile
        {...{
          data: productData?.data,
          isDraft,
          productData,
          onClickThreeDots,
          hasNextPage,
          isLoading: isProductLoading || isProductFetching,
          page,
          totalCount,
          checkedIds,
          onClickDelete,
          onClickPublish,
          onClickUnPublish,
          setCheckedIds,
          filters,
          setFilters,
          handleCheckItem,
          handleFormSubmit,
          setFormRef,
          fetchNextPage
        }}
      />
    );
  }

  return (
    <ProductsDesktop
      {...{
        isDraft,
        productData,
        onClickThreeDots,
        hasNextPage,
        isLoading: isProductLoading || isProductFetching,
        page,
        totalCount,
        checkedIds,
        onClickDelete,
        onClickPublish,
        onClickUnPublish,
        setCheckedIds,
        filters,
        setFilters,
        handleCheckItem,
        handleFormSubmit,
        setFormRef,
        fetchNextPage
      }}
    />
  );
}

export default Products;

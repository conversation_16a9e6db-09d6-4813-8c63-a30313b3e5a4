import i18n from "@/utils/i18n";
import * as yup from "yup";

export const validationSchema = yup.object({
  title: yup
    .string()
    .required(i18n.t("product.validations.required"))
    .min(5, i18n.t("product.validations.minLengthTitle"))
    .max(120, i18n.t("product.validations.maxChar", { amount: 120 })),
  description: yup.string().required(i18n.t("product.validations.required")),
  categoryId: yup.string().required(i18n.t("product.validations.required")),
  tags: yup.array().of(yup.string()),
  images: yup.array().of(
    yup
      .object()
      .shape({
        url: yup.string(),
        markedAsCover: yup.boolean()
      })
      .typeError(i18n.t("product.validations.imagesNotValid"))
  ),
  cover: yup.string().required(i18n.t("product.validations.required")),
  variants: yup
    .array(
      yup.object({
        id: yup
          .string()
          .typeError(i18n.t("product.validations.required"))
          .required(i18n.t("product.validations.required")),
        compareAtPrice: yup.number().nullable().typeError(i18n.t("product.validations.required")),
        // .required(i18n.t("product.validations.required")),
        salesPrice: yup
          .number()
          .typeError(i18n.t("product.validations.required"))
          .required(i18n.t("product.validations.required"))
          .min(1, i18n.t("supplier.profile.validations.positive"))
          .test("salesPrice-valid-range", i18n.t("product.validations.salesPriceOutOfRange"), function (value) {
            const index = parseInt(this.path.match(/\d+/)?.[0] ?? "-1", 10);
            const variantMeta = this.options?.context?.variantMeta?.[index];

            if (!variantMeta) return true;

            const { retailPrice, minimumRetailPrice } = variantMeta;

            const max = Number(retailPrice);
            const min = Number(minimumRetailPrice);

            return typeof value === "number" && value >= min && value <= max;
          }),

        isActive: yup.boolean()
        // .required(i18n.t("product.validations.required"))
      })
    )
    .min(1, i18n.t("product.validations.variantsNotCompleted"))
});

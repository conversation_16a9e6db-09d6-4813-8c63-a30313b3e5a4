"use client";

import React from "react";
import { Divider, MenuItem, SelectChangeEvent } from "@mui/material";
import { routes } from "@/constants/routes";
import { useTranslation } from "react-i18next";
import { CircularProgress } from "@mui/material";
import useRoleBasePath from "@/utils/hooks/useRoleBasePath";
import Link from "next/link";
import CustomTablePagination from "@/components/ui/CustomTablePagination/CustomTablePagination";
import { Icon } from "@iconify/react";
import Filters from "./Filters/Filters";
import CustomCheckbox from "@/components/ui/CustomCheckbox/CustomCheckbox";
import Button from "@/components/ui/Button";
import Collapse2 from "@/components/ui/Collapse2/Collapse2";
import Image from "next/image";
import useCurrency from "@/utils/hooks/useCurrency";
import { calcProfitAmount } from "@/components/containers/productDetailPage/utils";
import Edit from "./EditPage/EditPage";
import CustomMenu from "@/components/ui/CustomMenu/CustomMenu";
import { useConversationStart } from "@/utils/hooks/useConversationStart";
import ProductEmpty from "../productEmpty/ProductEmpty";
import { ProductsDesktopProps } from "./types";

function ProductsDesktop({
  isDraft = false,
  isLoading,
  page,
  productData,
  totalCount,
  checkedIds,
  onClickDelete,
  onClickPublish,
  onClickUnPublish,
  setCheckedIds,
  filters,
  setFilters,
  handleCheckItem,
  handleFormSubmit,
  setFormRef
}: ProductsDesktopProps) {
  /* ---------------------------------- hooks --------------------------------- */
  const { t } = useTranslation();
  const [{ render: renderPrice, symbol }] = useCurrency();
  const makePath = useRoleBasePath();
  const { onChatStart, isConversationLoading } = useConversationStart();

  const handleChangePage = (_event: unknown, newPage: number) => {
    setFilters({ page: newPage }, { history: "push" });
  };

  const handleChangeRowsPerPage = (event: SelectChangeEvent<number>) => {
    setFilters({ pageSize: event.target.value as number }, { history: "push" });
    setFilters({ page: 1 }, { history: "push" });
  };

  const handleCheckAllItemsInThisPage = (checked: boolean) => {
    const ids = productData?.data
      // ?.filter(item => !checkedIds?.find(v => v === item?.id))
      ?.map(item => item.id) as string[];

    setCheckedIds(checked ? ids : []);
  };

  // Show loading only on initial load
  if (isLoading && page === 1) {
    return (
      <div className="flex items-center justify-center">
        <CircularProgress />
      </div>
    );
  }

  return (
    <div className="h-full flex flex-col">
      {productData?.data?.length && (
        <div className="mb-4 ">
          <Filters
            {...{
              handleCheckAllItems: handleCheckAllItemsInThisPage,
              totalCount,
              checkedIds,
              onClickDelete,
              onClickPublish,
              onClickUnPublish,
              isDraft,
              productData
            }}
          />
        </div>
      )}

      <div className="flex flex-col gap-2">
        {!productData?.data?.length && !isLoading ? (
          <ProductEmpty isDraft={isDraft} hasSearchValue={false} />
        ) : (
          productData?.data?.map((item, index) => {
            const retailerProfitAmount = calcProfitAmount({
              commission: item?.originProduct?.cheapestVariant?.commission,
              retailPrice: item?.originProduct?.cheapestVariant?.retailPrice
            });

            const salesPrice = [...(item?.variants || [])]?.sort(
              (a, b) => Number(a?.salesPrice) - Number(b?.salesPrice)
            )?.[0]?.salesPrice;

            return (
              <Collapse2
                variant="primary"
                key={item?.id}
                initialIsOpen={isDraft && index === 0 && filters?.page === 1}
                startAdornment={
                  <div className="flex gap-3 items-center ms-1">
                    <div>
                      <CustomCheckbox
                        onChange={(_e, _checked) => handleCheckItem(item.id)}
                        checked={!!checkedIds?.length && checkedIds?.some(v => v === item?.id)}
                        className="p-0 !m-0"
                        checkboxClassName="p-0.5"
                      />
                    </div>
                    <div className="bg-v2-surface-secondary size-[46px] rounded-md relative">
                      <Image
                        src={item?.originProduct?.cover?.url}
                        alt={item?.originProduct?.cover?.alt || item.title}
                        fill
                        className="object-contain"
                      />
                    </div>

                    <div className="flex flex-col gap-1.5">
                      <div className="text-sm font-medium text-v2-content-primary">{item.title}</div>
                      <div className="flex items-center divide-x  divide-x-reverse  divide-gray-50 *:px-3">
                        <div className="!pr-0 text-v2-content-tertiary text-xs font-normal">
                          {t("retailerImport.salesPrice")}:{" "}
                          {salesPrice && (
                            <span className="font-medium text-v2-content-primary">{renderPrice(salesPrice)}</span>
                          )}
                        </div>
                        {!!retailerProfitAmount && (
                          <div className="text-v2-content-tertiary text-xs font-normal">
                            {t("retailerProduct.yourProfit")}:{" "}
                            <span className="font-medium text-v2-content-primary">
                              {renderPrice(retailerProfitAmount)}
                            </span>
                          </div>
                        )}
                        <div className="text-v2-content-tertiary text-xs font-normal">
                          {t("retailerProduct.supplier")}:{" "}
                          <span className="font-medium">{item?.originProduct?.supplier?.name}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                }
                endAdornment={
                  <div className="flex items-center gap-3 xmd:flex-row flex-row-reverse">
                    {/* {hasError && (
                      <div className="flex items-center px-2 py-1 gap-1 rounded bg-v2-surface-error">
                        <Icon icon="solar:danger-bold" className="text-v2-content-on-error-2 size-4" />
                        <span className="text-v2-content-on-error-2 text-body4-medium">{t("error")}</span>
                      </div>
                    )} */}
                    <CustomMenu
                      content={
                        <>
                          <MenuItem className="pb-3.5 pt-1.5 text-[13px]">{t("retailerProduct.buySample")}</MenuItem>

                          <Divider className="bg-gray-50 !my-0" />

                          <Link href={`${makePath(routes.product)}/${item.originProduct?.id}`} target="_blank">
                            <MenuItem className="py-3.5 text-[13px]">{t("retailerProduct.productPage")}</MenuItem>
                          </Link>

                          <Divider className="bg-gray-50 !my-0" />

                          <MenuItem
                            className="py-3.5 text-[13px]"
                            onClick={() =>
                              !isConversationLoading &&
                              onChatStart({
                                content: t("chats.product", { name: item?.title }),
                                partnerId: item?.originProduct?.supplierId
                              })
                            }
                          >
                            {t("retailerProduct.contactWithSupplier")}
                          </MenuItem>

                          {/* {!isDraft && (
                            <>
                              <Divider className="bg-gray-50 !my-0" />

                              <MenuItem
                                className="py-3.5 text-[13px] text-v2-content-on-error-2"
                                onClick={() => onClickUnPublish([item?.id])}
                              >
                                {t("retailerProduct.unPublish")}
                              </MenuItem>
                            </>
                          )} */}

                          <Divider className="bg-gray-50 !my-0" />

                          <MenuItem
                            className="py-3.5 text-[13px] text-v2-content-on-error-2"
                            onClick={() => !isConversationLoading && onClickDelete([item?.id])}
                          >
                            {t("retailerProduct.removeFromStore")}
                          </MenuItem>
                        </>
                      }
                    >
                      <div className="flex items-center justify-center p-2.5 border border-v2-border-primary rounded-lg cursor-pointer">
                        <Icon icon="solar:menu-dots-bold" width={20} height={20} className="rotate-90" />
                      </div>
                    </CustomMenu>

                    <>
                      {isDraft && (
                        <Button
                          variant="primary"
                          className="xmd:flex-auto flex-1"
                          onClick={() => handleFormSubmit(item.id)}
                        >
                          {t("retailerProduct.addToShop")}
                        </Button>
                      )}
                    </>
                  </div>
                }
                rootClassName="rounded-lg bg-v2-surface-primary aria-expanded:pt-[11px] transition-all"
                className="py-4 px-5 !bg-transparent aria-expanded:border-b aria-expanded:border-v2-border-secondary "
              >
                <Edit ref={ref => setFormRef(item.id, ref)} product={item} />
              </Collapse2>
            );
          })
        )}
      </div>

      {!!totalCount && totalCount >= 10 && (
        <div className="bg-v2-surface-primary mt-2 py-2 px-6 rounded-lg">
          {productData?.data && (
            <CustomTablePagination
              rowsPerPageOptions={[10, 50, 100, 200]}
              count={totalCount}
              rowsPerPage={filters?.pageSize}
              page={filters?.page}
              onPageChange={handleChangePage}
              onRowsPerPageChange={handleChangeRowsPerPage}
              labelRowsPerPage={t("product.rowPerPage")}
            />
          )}
        </div>
      )}
    </div>
  );
}

export default ProductsDesktop;

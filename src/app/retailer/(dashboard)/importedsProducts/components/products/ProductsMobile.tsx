"use client";

import React, { useCallback, useEffect, useState } from "react";
import { Divider } from "@mui/material";
import { useTranslation } from "react-i18next";
import { Icon } from "@iconify/react";
import CustomCheckbox from "@/components/ui/CustomCheckbox/CustomCheckbox";
import Button from "@/components/ui/Button";
import { handleSetFilter } from "@/utils/helpers";
import Collapse2 from "@/components/ui/Collapse2/Collapse2";
import Image from "next/image";
import useCurrency from "@/utils/hooks/useCurrency";
import { calcProfitAmount } from "@/components/containers/productDetailPage/utils";
import Edit from "./EditPage/EditPage";
import { twMerge } from "tailwind-merge";
import Input from "@/components/ui/inputs/Input";
import { debounce } from "lodash";
import BottomAction from "@/components/ui/bottomAction/BottomAction";
import { ListInfiniteScroll } from "@/components/ui/ListInfiniteScroll";
import { TRetailerImportListData } from "@/store/apps/retailerProduct/types";
import ProductEmpty from "../productEmpty/ProductEmpty";
import { ProductsMobileProps } from "./types";

function ProductsMobile({
  isDraft = false,
  hasNextPage,
  data,
  fetchNextPage,
  isLoading,
  page,
  setFilters,
  filters,
  checkedIds,
  setCheckedIds,
  onClickDelete,
  onClickPublish,
  handleCheckItem,
  handleFormSubmit,
  onClickThreeDots,
  setFormRef
}: ProductsMobileProps) {
  /* ---------------------------------- hooks --------------------------------- */
  const [internalData, setInternalData] = useState<TRetailerImportListData["data"] | undefined>();
  const { t } = useTranslation();
  const [{ render: renderPrice, symbol }] = useCurrency();

  useEffect(() => {
    // if ((initialData?.length && !internalData?.length) || initialData?.length !== internalData?.length) {
    setInternalData((prev: any) => [...(prev || []), ...(data || [])]);
    // }
  }, [JSON.stringify(data)]);

  const handleInView = () => {
    if (!isLoading && hasNextPage) {
      fetchNextPage?.()?.then((res: any) => {
        if (!res?.length) return;

        setInternalData((prev: any) => [...(prev || []), ...res]);
      });
    }
  };

  const handleCheckAllItems = (checked: boolean) => {
    const ids = internalData
      // ?.filter(item => !checkedIds?.find(v => v === item?.id))
      ?.map(item => item.id) as string[];

    setCheckedIds(checked ? ids : []);
  };

  const [hasCheckbox, setHasCheckbox] = useState(false);

  const handleOnChange = useCallback(
    debounce((value: string) => {
      // setFilters({ title: value, page: 1 }, { history: "push" });
      handleSetFilter({ key: "title", value, setFilters });
    }, 2000),
    []
  );
  const internalValue = filters?.title;

  return (
    <div className="h-full">
      <div className="flex flex-col px-4 w-[100vw] ">
        {!!internalData?.length && (
          <div className="mb-3">
            <Input
              startAdornment={
                <Icon icon="solar:magnifer-linear" width="1.1rem" color="#ADADAD" height="1.1rem" className="ml-1.5" />
              }
              inputSize="sm"
              variant="filled"
              className="max-h-10 "
              rootClassName=" shrink-0 "
              inputParentClassName="!bg-v2-surface-primary"
              value={internalValue || undefined}
              placeholder={`${t("chats.searchQuery")} ...`}
              onChange={e => {
                handleOnChange(e.target.value);
              }}
            />
          </div>
        )}

        {!!internalData?.length && (
          <div className="mb-5">
            {hasCheckbox ? (
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <CustomCheckbox
                    className="!p-0 !ml-0 !mr-0 [&>span]:!p-0"
                    indeterminate={!!checkedIds?.length && checkedIds?.length !== internalData?.length}
                    onChange={(_e, checked) => (checked ? handleCheckAllItems(checked) : setCheckedIds([]))}
                    checked={!!checkedIds?.length && checkedIds?.length === internalData?.length}
                  />

                  <span className="text-v2-content-primary text-body3-medium whitespace-nowrap">{t("selectAll")}</span>

                  {!!checkedIds?.length && (
                    <span className="text-body3-medium text-v2-content-tertiary whitespace-nowrap">
                      ({t("selected")} {checkedIds?.length})
                    </span>
                  )}
                </div>
                <p
                  className="text-v2-content-on-action-2 text-body3-medium cursor-pointer"
                  onClick={() => setHasCheckbox(false)}
                >
                  {t("done")}
                </p>
              </div>
            ) : (
              <p
                className="text-v2-content-on-action-2 text-body3-medium cursor-pointer text-end"
                onClick={() => setHasCheckbox(true)}
              >
                {t("select")}
              </p>
            )}
          </div>
        )}

        <div className="flex flex-col gap-2 w-full flex-1 ">
          {!internalData?.length && !isLoading && !hasNextPage ? (
            <ProductEmpty isDraft={isDraft} hasSearchValue={!!internalValue?.length} />
          ) : (
            internalData?.map((item, index) => {
              const retailerProfitAmount = calcProfitAmount({
                commission: item?.originProduct?.cheapestVariant?.commission,
                retailPrice: item?.originProduct?.cheapestVariant?.retailPrice
              });

              return (
                <div
                  key={item?.id}
                  className={twMerge(
                    "grid gap-2.5 w-full",
                    hasCheckbox ? "grid-cols-[30px_minmax(0,1fr)]" : "grid-cols-1"
                  )}
                >
                  {hasCheckbox && (
                    <div>
                      <CustomCheckbox
                        onChange={(_e, _checked) => handleCheckItem(item.id)}
                        checked={!!checkedIds?.length && checkedIds?.some(v => v === item?.id)}
                        className="p-0 !m-0"
                        checkboxClassName="p-0.5"
                      />
                    </div>
                  )}

                  <Collapse2
                    variant="secondary"
                    key={item?.id}
                    initialIsOpen={isDraft && index === 0 && page === 1}
                    startAdornment={
                      <div className="flex gap-3 items-center ms-1">
                        <div className="bg-v2-surface-secondary size-[64px] rounded-md relative">
                          <Image
                            src={item?.originProduct?.cover?.url}
                            alt={item?.originProduct?.cover?.alt || item.title}
                            fill
                            className="object-contain"
                          />
                        </div>

                        <div className="flex flex-col gap-1.5">
                          <p className="text-sm font-medium text-v2-content-primary line-clamp-1  ">{item.title}</p>
                          <div className="flex items-center gap-3 flex-wrap">
                            <div className="!pr-0 text-v2-content-tertiary text-xs font-normal flex flex-col gap-1">
                              <p>{t("retailerImport.salesPrice")} : </p>
                              <p className="font-medium text-v2-content-primary">
                                {renderPrice(item?.originProduct?.cheapestVariant?.retailPrice)}
                              </p>
                            </div>

                            <Divider
                              orientation="vertical"
                              variant="middle"
                              flexItem
                              className=" border-v2-border-primary h-4 mt-3"
                            />

                            {!!retailerProfitAmount && (
                              <>
                                <div className="text-v2-content-tertiary text-xs font-normal flex flex-col gap-1">
                                  <p>{t("retailerProduct.yourProfit")} : </p>
                                  <p className="font-medium text-v2-content-primary">
                                    {renderPrice(retailerProfitAmount)}
                                  </p>
                                </div>

                                <Divider
                                  orientation="vertical"
                                  variant="middle"
                                  flexItem
                                  className=" border-v2-border-primary h-4 mt-3"
                                />
                              </>
                            )}

                            <div className="text-v2-content-tertiary text-xs font-normal flex flex-col gap-1">
                              <p>{t("retailerProduct.supplier")} : </p>
                              <p className="font-medium">{item?.originProduct?.supplier?.name}</p>
                            </div>
                          </div>
                        </div>
                      </div>
                    }
                    endAdornment={
                      <div className="flex items-center gap-3 ">
                        {isDraft && (
                          <Button
                            variant="primary"
                            className="xmd:flex-auto flex-1"
                            onClick={() => handleFormSubmit(item.id)}
                          >
                            {t("retailerProduct.addToShop")}
                          </Button>
                        )}

                        <div
                          className="flex items-center justify-center p-2.5 border border-v2-border-primary rounded-lg cursor-pointer"
                          onClick={() => onClickThreeDots(item)}
                        >
                          <Icon icon="solar:menu-dots-bold" width={20} height={20} className="rotate-90" />
                        </div>
                      </div>
                    }
                    rootClassName="rounded-lg bg-v2-surface-primary aria-expanded:pt-[11px] transition-all"
                    className="py-4 px-5 !bg-transparent aria-expanded:border-b aria-expanded:border-v2-border-secondary "
                  >
                    <Edit ref={ref => setFormRef(item.id, ref)} product={item} />
                  </Collapse2>
                </div>
              );
            })
          )}
        </div>
      </div>

      {isDraft && !!checkedIds?.length && (
        <BottomAction containerClassName="!z-[999999] gap-6">
          <Button
            variant="destructiveSecondaryGray"
            className="text-v2-content-on-error-2"
            onClick={() => onClickDelete(checkedIds)}
          >
            <Icon icon="solar:trash-bin-minimalistic-outline" width={20} height={20} />
            حذف محصولات
          </Button>
          <Button
            variant="destructiveSecondaryGray"
            className="text-v2-content-secondary"
            onClick={() => onClickPublish(checkedIds)}
          >
            <Icon icon="ph:plus" width={20} height={20} />
            اضافە بە فروشگاه
          </Button>
        </BottomAction>
      )}

      <ListInfiniteScroll hasNextPage={hasNextPage} fetchNextPage={handleInView} />
    </div>
  );
}

export default ProductsMobile;

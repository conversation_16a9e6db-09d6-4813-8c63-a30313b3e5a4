import CustomButton from "@/components/ui/CustomButton/CustomButton";
import { ShipmentDetail, TOrderData, TOrderResponse } from "@/store/apps/order/types";
import { ensureUrlScheme } from "@/utils/helpers";
import { Icon } from "@iconify/react";
import { Avatar } from "@mui/material";
import Link from "next/link";
import { useState } from "react";
import { useTranslation } from "react-i18next";
import { twMerge } from "tailwind-merge";

interface IOrderDetailShipmentProps {
  shipItem: ShipmentDetail;
  retailerOrder?: { data: TOrderData };
}

function OrderDetailShipment({ shipItem, retailerOrder }: IOrderDetailShipmentProps) {
  const { t } = useTranslation();
  const [isCollapse, setIsCollapse] = useState(false);

  const shippingPhotos = (sid: string) =>
    retailerOrder?.data?.lineItems?.find(item => item?.supplierId === sid)?.product?.images?.filter(Boolean);

  const shippingSupplier = (sid: string) =>
    retailerOrder?.data?.lineItems?.find(item => item?.supplierId === sid)?.product;

  const onDownload = async () => {
    const docUrl = shipItem?.document?.url;
    const filename = shipItem?.document?.user;

    if (!docUrl || !filename) {
      console.error("Invalid document URL or filename");
      return;
    }

    try {
      const response = await fetch(docUrl);
      if (!response.ok) throw new Error("Failed to fetch file");

      const fileBlob = await response.blob();

      const fileExtension = docUrl.split(".").pop() || "pdf";
      const mimeType = response.headers.get("content-type") || "application/octet-stream";

      const tempLink = document.createElement("a");
      tempLink.href = URL.createObjectURL(fileBlob);
      tempLink.setAttribute("download", `${filename}.${fileExtension}`);

      document.body.appendChild(tempLink);
      tempLink.click();

      /* --------------------------------- Cleanup -------------------------------- */
      setTimeout(() => {
        try {
          if (document.body.contains(tempLink)) {
            document.body.removeChild(tempLink);
          }
          window.URL.revokeObjectURL(tempLink.href);
        } catch (error) {
          console.warn("Download cleanup error:", error);
        }
      }, 100);
    } catch (err) {
      console.error("Error downloading file:", err);
    }
  };

  return (
    <div
      key={shipItem?.carrierId}
      className="flex flex-col pb-4 border-b border-b-v2-border-secondary last-of-type:border-b-transparent"
    >
      <div className="flex items-center gap-2 flex-wrap  ">
        {shipItem?.documentId && (
          <div
            className="border border-v2-border-primary rounded shrink-0 p-1 flex items-center justify-center cursor-pointer"
            onClick={() => setIsCollapse(prev => !prev)}
          >
            <Icon icon={isCollapse ? "ep:arrow-up" : "ep:arrow-down"} className="size-4 text-v2-content-primary" />
          </div>
        )}

        {/* <div className="flex items-center justify-between"> */}
        {/* <div key={shipItem?.carrierId} className="flex  items-start xmd:gap-x-20 gap-y-5 gap-x-10 xmd:mt-4 mt-0 "> */}
        <div className="flex flex-col xmd:me-20 me-10">
          <span className="text-body4-regular text-v2-content-tertiary whitespace-nowrap">
            {" "}
            {t("order.shippingType")}
          </span>
          <span className="text-body4-medium text-v2-content-primary whitespace-nowrap">
            {" "}
            {shipItem?.carrier?.name}
          </span>
        </div>

        <div className="flex flex-col xmd:me-20 me-10">
          <span className="text-body4-regular text-v2-content-tertiary whitespace-nowrap"> {t("order.supplier")}</span>
          <span className="text-body4-medium text-v2-content-primary whitespace-nowrap">
            {" "}
            {shippingSupplier(shipItem?.supplierId)?.supplier?.name ?? "-"}
          </span>
        </div>

        <div className="flex flex-col">
          <span className="text-body4-regular text-v2-content-tertiary whitespace-nowrap">
            {" "}
            {t("order.trackingCode")}
          </span>
          <div className="flex items-center gap-1">
            <span className="text-body4-regular text-v2-content-primary mt-1 whitespace-nowrap">
              {" "}
              {shipItem?.trackingCode}
            </span>
            <Link
              target="_blank"
              rel="noopener noreferrer"
              href={ensureUrlScheme(shipItem?.trackingUrl)}
              className="text-v2-content-on-action-2 whitespace-nowrap"
            >
              <span className="text-caption-medium">{t("retailerOrder.orderSummary.tracking")}</span>
            </Link>
          </div>
        </div>

        <div className="flex items-center gap-2 flex-1 justify-end">
          {shippingPhotos(shipItem?.supplierId)?.map((shipPhoto, index) => (
            <Avatar key={index} src={shipPhoto?.url} alt={shipPhoto?.alt} className="size-[46px] rounded-lg" />
          ))}
        </div>
      </div>

      {/* </div> */}
      {/* </div> */}
      {shipItem?.documentId && (
        <div className={twMerge("p-4 rounded-lg bg-v2-surface-secondary mt-4", isCollapse ? "block" : "hidden")}>
          <p className="mb-2 text-v2-content-secondary text-caption-medium">{t("description")} : </p>
          <p className="text-body3-medium text-v2-content-primary">{shipItem?.note} </p>

          <div className="p-4 mt-4 border border-v2-border-primary bg-cards rounded-[10px] flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="p-3 rounded-full border border-v2-border-primary w-fit">
                <Icon icon="solar:file-outline" className="size-6" />
              </div>

              <div className="flex flex-col">
                <span className="text-body2-medium text-v2-content-primary">{shipItem?.document?.key}</span>
                {/* <span className="text-v2-content-tertiary text-caption-regular ">size</span> */}
              </div>
            </div>
            <CustomButton
              color="secondary"
              onClick={onDownload}
              startIcon={<Icon icon="solar:cloud-download-outline" className="text-v2-content-primary size-[18px]" />}
            >
              {t("download")}
            </CustomButton>
          </div>
        </div>
      )}
    </div>
  );
}

export default OrderDetailShipment;

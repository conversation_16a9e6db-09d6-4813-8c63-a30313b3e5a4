import { supplierShippingSchema } from "@/utils/validations/profile/supplier";
import { Grid, CircularProgress } from "@mui/material";
import React, { useState } from "react";
import { useTranslation } from "react-i18next";
import { getShippingRateTypeItems, getShippingTimeItems, getShippingTypeItems } from "./utils";
import CustomAutocomplete from "@/components/ui/CustomAutocomplete/CustomAutocomplete";
import { extractMinMax, findItemByMinMax } from "@/components/containers/ProfileStepper/utils";
import LocationsSelect from "@/components/ui/CustomCountrySelect/LocationsSelect";
import CustomButton from "@/components/ui/CustomButton/CustomButton";
import { useGetMetaCarrierQuery } from "@/store/apps/meta";
import { TSupplierShippingData } from "@/store/apps/supplier/types";
import "./SupplierShipping.css";
import useModal from "@/utils/hooks/useModal";
import { Icon } from "@iconify/react";
import CustomSwitch from "@/components/ui/CustomSwitch/CustomSwitch";
import { ISupplierShippingModalProps } from "./types";
import { useForm, Controller } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import { SetHookFormError } from "@/utils/services/utils";
import BottomAction from "@/components/ui/bottomAction/BottomAction";
import MobileAppBar from "@/components/containers/mobileAppBar/MobileAppBar";
import PriceInput from "@/components/ui/inputs/PriceInput";
import Textarea from "@/components/ui/inputs/Textarea/Textarea";

const SupplierShippingFormModal = ({
  initialValues,
  initialValue,
  handleSubmit,
  isDisabled,
  isEdit,
  onBack,
  hasBottomAction
}: ISupplierShippingModalProps) => {
  const { t } = useTranslation();
  const { hideModal } = useModal();
  const shippingTimeItems = getShippingTimeItems({ t });
  const shippingRateTypeItems = getShippingRateTypeItems({ t });
  const shippingTypeItems = getShippingTypeItems({ t });
  const { data: carriers } = useGetMetaCarrierQuery();
  const shippingCarrierItems = carriers?.data?.map(item => ({ id: item?.id, label: item?.name }));
  const [isLoading, setIsLoading] = useState(false);

  const policiesData = {
    ...initialValue,
    shippingTo: initialValue?.shippingTo ? initialValue?.shippingTo : "00000000-0000-0000-0000-000000000000"
  };
  const excludeIds = initialValues?.length
    ? (initialValues?.map(item => item?.shippingTo || "00000000-0000-0000-0000-000000000000") as string[])
    : [];
  const finalInitialValues = policiesData?.shippingCarrierId
    ? policiesData
    : {
        description: undefined,
        rate: undefined,
        prepaid: undefined,
        rateType: undefined,
        shippingFrom: undefined,
        shippingTime: undefined,
        shippingTo: excludeIds?.includes("00000000-0000-0000-0000-000000000000")
          ? ""
          : "00000000-0000-0000-0000-000000000000",
        excluded: false,
        extraItemRate: undefined,
        shippingCarrierId: undefined
      };

  const freePrice =
    policiesData?.rate && policiesData?.extraItemRate && +policiesData?.rate <= 0 && +policiesData?.extraItemRate <= 0;
  const [isFree, setIsFree] = useState(!!freePrice && true);

  const {
    control,
    handleSubmit: handleFormSubmit,
    formState: { errors, isValid },
    setValue,
    setError,
    watch
  } = useForm<TSupplierShippingData>({
    defaultValues: finalInitialValues,
    resolver: yupResolver(supplierShippingSchema(isFree)) as any,
    mode: "onChange"
  });

  const excluded = watch("excluded");

  const onSubmit = (value: TSupplierShippingData) => {
    const values = initialValues?.length ? initialValues : [];
    const findExist = initialValues?.find(item => item?.id === value?.id);
    const res = isEdit ? initialValues?.map(item => (item?.id === findExist?.id ? value : item)) : [...values, value];
    const finalResult = res?.map(
      ({ id = "", shippingCarrier = "", shippingLocation = {}, shippingTo = "", ...item }) => ({
        ...item,
        extraItemRate: item?.extraItemRate || "0",
        rate: item?.rate || "0",
        shippingTo: shippingTo?.includes("00000000-0000-0000-0000-000000000000") ? "" : shippingTo
      })
    );

    setIsLoading(true);
    handleSubmit({ policies: finalResult as TSupplierShippingData[] }, setError as SetHookFormError).finally(() =>
      setIsLoading(false)
    );
  };

  const watchPrepaid = watch("prepaid");

  return (
    <div>
      <form onSubmit={handleFormSubmit(onSubmit)}>
        <div className="xmd:flex hidden items-center gap-2 xmd:-mt-6 mt-2.5 mb-6">
          <Icon icon="solar:delivery-outline" className="size-6" />
          <span className="text-gray-999 text-body4-medium">{t("supplier.profile.shippingModal")}</span>
        </div>
        <div>
          <Grid container spacing={{ xs: 1, sm: 1.5 }}>
            <Grid item xs={12} md={6}>
              <Controller
                name="shippingTo"
                control={control}
                render={({ field }) => (
                  <LocationsSelect
                    {...field}
                    multiple={false}
                    label={t("supplier.profile.shippingTo")}
                    placeholder={t("supplier.profile.shippingTo")}
                    hasAllCities
                    excludeIds={excludeIds}
                    inputValue={finalInitialValues?.shippingTo}
                    handleBlur={field.onBlur}
                    onChange={value => setValue("shippingTo", value as string)}
                    error={!!errors.shippingTo?.message}
                    helperText={errors.shippingTo?.message || ""}
                  />
                )}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <Controller
                name="shippingCarrierId"
                control={control}
                render={({ field }) => (
                  <CustomAutocomplete
                    {...field}
                    optional={excluded}
                    requiredStar={!excluded}
                    value={shippingCarrierItems?.find(item => item.id === field.value)}
                    options={shippingCarrierItems || []}
                    label={t("supplier.profile.shippingCarrier")}
                    placeholder={t("supplier.profile.shippingCarrier")}
                    onChange={(e, value) => {
                      setValue("shippingCarrierId", value?.id);
                      // field.onChange(e);
                    }}
                    error={!!errors.shippingCarrierId}
                    helperText={errors.shippingCarrierId?.message || ""}
                  />
                )}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <Controller
                name="prepaid"
                control={control}
                render={({ field }) => (
                  <CustomAutocomplete
                    {...field}
                    optional={excluded}
                    requiredStar={!excluded}
                    value={shippingTypeItems?.find(item => item.id === field.value)}
                    options={shippingTypeItems}
                    label={t("supplier.profile.priceType")}
                    placeholder={t("supplier.profile.priceType")}
                    onChange={(e, value) => {
                      // field.onChange(e);
                      setValue("prepaid", value?.id);

                      setValue("rate", !value?.id ? "0" : "");
                      setValue("extraItemRate", !value?.id ? "0" : "");
                      if (isFree && value?.id) {
                        setIsFree(false);
                      }
                    }}
                    error={!!errors.prepaid?.message}
                    helperText={errors.prepaid?.message || ""}
                  />
                )}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <Controller
                name="shippingTime"
                control={control}
                render={({ field }) => (
                  <CustomAutocomplete
                    {...field}
                    optional={excluded}
                    requiredStar={!excluded}
                    value={findItemByMinMax(shippingTimeItems, field.value?.min, field.value?.max)}
                    options={shippingTimeItems}
                    label={t("supplier.profile.shippingTime")}
                    placeholder={t("supplier.profile.shippingTime")}
                    onChange={(e, value) => {
                      // field.onChange({ ...e, target: { ...e.target, value: extractMinMax(value?.id as string) } });
                      setValue("shippingTime", extractMinMax(value?.id as string));
                    }}
                    error={!!errors.shippingTime?.message}
                    helperText={errors.shippingTime?.message || ""}
                  />
                )}
              />
            </Grid>
            {watchPrepaid !== false && (
              <>
                <Grid item xs={12} md={12}>
                  <div className="flex flex-col xmd:mt-4 mt-1">
                    <span className="text-body4-medium text-gray-999">{t("supplier.profile.shippingFreeTitle")}</span>
                    <span className="text-caption-regular text-gray-600">
                      {t("supplier.profile.shippingFreeSubtitle")}
                    </span>
                  </div>
                  <div className="xmd:mt-4 mt-3 xmd:mb-0 mb-3">
                    <div className="flex items-center gap-2">
                      <CustomSwitch
                        labelClassName="!m-0 "
                        textClassName="!m-0"
                        checked={isFree}
                        onChange={(e, checked) => {
                          setIsFree(checked);
                          setValue("rate", checked ? "0" : "");
                          setValue("extraItemRate", checked ? "0" : "");
                        }}
                      />
                      <span className="text-caption-medium text-gray-999">
                        {t("supplier.profile.shippingFreeSwitch")}
                      </span>
                    </div>
                  </div>
                </Grid>
                {!isFree && (
                  <>
                    <Grid item xs={12} md={6}>
                      <Controller
                        name="rate"
                        control={control}
                        render={({ field }) => (
                          <PriceInput
                            {...field}
                            hasCommaSeparator
                            autoComplete="off"
                            optional={excluded}
                            requiredStar={!excluded}
                            label={t("supplier.profile.basePrice")}
                            placeholder={t("supplier.profile.basePrice")}
                            className="custom-field"
                            // FormHelperTextProps={{ sx: { display: "contents" } }}
                            error={!!errors.rate?.message}
                            helperText={errors.rate?.message || ""}
                          />
                        )}
                      />
                    </Grid>
                    <Grid item xs={12} md={6}>
                      <Controller
                        name="extraItemRate"
                        control={control}
                        render={({ field }) => (
                          <PriceInput
                            {...field}
                            hasCommaSeparator
                            autoComplete="off"
                            requiredStar={false}
                            optional
                            label={t("supplier.profile.extraItemPrice")}
                            placeholder={t("supplier.profile.extraItemPrice")}
                            className="custom-field"
                            // FormHelperTextProps={{ sx: { display: "contents" } }}
                            error={!!errors?.extraItemRate?.message}
                            helperText={errors?.extraItemRate?.message || ""}
                          />
                        )}
                      />
                    </Grid>
                  </>
                )}
              </>
            )}
            {/* <Grid item xs={12} md={12} className="h-[108px] xmd:h-auto xmd:!pt-4">
              <Controller
                name="description"
                control={control}
                render={({ field }) => (
                  <Textarea
                    {...field}
                    rows={2}
                    autoComplete="off"
                    requiredStar={false}
                    optional
                    label={t("supplier.profile.description")}
                    placeholder={t("supplier.profile.placeholders.shippingDescription")}
                    error={!!errors.description?.message}
                    helperText={errors.description?.message || ""}
                  />
                )}
              />
            </Grid> */}

            {hasBottomAction ? (
              <BottomAction
                containerClassName="!z-[999]"
                saveButtonText={
                  isLoading ? <CircularProgress color="info" size={20} /> : isEdit ? t("saveChanges") : t("add")
                }
                saveButtonProps={{
                  type: "submit",
                  disabled: isLoading
                }}
                cancelButtonText={t("supplier.profile.cancel")}
                cancelButtonProps={{
                  onClick: () => {
                    hideModal();
                    onBack?.();
                  }
                }}
              />
            ) : (
              <Grid
                className="xmd:hidden flex"
                item
                display="flex"
                gap={2}
                justifyContent="flex-end"
                xs={12}
                md={12}
                mt={3}
              >
                <CustomButton fullWidth color="secondary" onClick={() => hideModal()}>
                  {t("supplier.profile.cancel")}
                </CustomButton>
                <CustomButton fullWidth onClick={handleFormSubmit(onSubmit)} disabled={isLoading} color="primary">
                  {isLoading ? <CircularProgress color="info" size={20} /> : t("supplier.profile.save")}
                </CustomButton>
              </Grid>
            )}

            <Grid
              className="xmd:flex hidden"
              item
              display="flex"
              gap={2}
              justifyContent="flex-end"
              xs={12}
              md={12}
              mt={3}
            >
              <CustomButton fullWidth color="secondary" onClick={() => hideModal()}>
                {t("supplier.profile.cancel")}
              </CustomButton>
              <CustomButton fullWidth type="submit" disabled={isLoading} color="primary">
                {isLoading ? <CircularProgress color="info" size={20} /> : t("supplier.profile.save")}
              </CustomButton>
            </Grid>
          </Grid>
        </div>
      </form>
    </div>
  );
};

export default SupplierShippingFormModal;

import ExcelUploader from "@/components/containers/ImageUploader/withUi/ExcelUploader";
import Button from "@/components/ui/Button";
import InputHelper from "@/components/ui/CustomFormHelperText/InputHelper";
import { useProductImportMutation } from "@/store/apps/product";
import useModal from "@/utils/hooks/useModal";
import { Icon } from "@iconify/react";
import { CircularProgress } from "@mui/material";
import Link from "next/link";
import { useState } from "react";
import { useTranslation } from "react-i18next";

function ExcelUploadModal() {
  const { t } = useTranslation();
  const { hideModal } = useModal();
  const [error, setError] = useState<string | undefined>(undefined);
  const [url, setUrl] = useState("");

  const [productImport, { isLoading: isImportLoading }] = useProductImportMutation();

  const onErrorAttachUploadedFileToRetailerProfile = (errorMessage?: string) => {
    setTimeout(() => {
      setError(t(`${errorMessage}`));
    }, 0);
  };

  const handleDownloadSampleFile = (url: string, name: string) => {
    const link = document.createElement("a");
    link.href = url;
    link.download = name;
    link.target = "_blank";
    document.body.appendChild(link);
    link.click();

    /* ---- Use setTimeout to ensure the download has started before cleanup ---- */
    setTimeout(() => {
      try {
        if (document.body.contains(link)) {
          document.body.removeChild(link);
        }
      } catch (error) {
        console.warn("Download cleanup error:", error);
      }
    }, 100);
  };

  const onUpload = async () => {
    setError(undefined);
    if (url) {
      try {
        const res: any = await productImport({ body: { url: url } });

        const status = (res as any)?.error?.status;

        if (status === 422 || status === 500) {
          setError(t("noProductsExcel"));
        }

        if (res?.data) {
          hideModal();
          setUrl("");
          setError(undefined);
        }
      } catch (err: any) {
        //
      }
    }
  };

  return (
    <>
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2 ">
          <Icon icon="solar:cloud-upload-linear" className="size-6 text-v2-content-primary" />
          <span className="text-v2-content-primary text-body3-medium">{t("product.excelUpload.title")}</span>
        </div>

        <Icon
          icon="material-symbols-light:close-rounded"
          className="size-5 text-v2-content-tertiary cursor-pointer"
          onClick={hideModal}
        />
      </div>

      <div className="mt-4">
        <span className="text-body4-medium text-v2-content-primary">{t("product.excelUpload.subtitle1")}</span>

        <div className="flex items-center mt-4 justify-between">
          <div className="flex items-center xmd:gap-6 gap-3">
            <div
              className="flex items-center gap-1 cursor-pointer"
              onClick={() =>
                handleDownloadSampleFile(process.env.NEXT_PUBLIC_SAMPLE_EXCEL_DOWNLOAD_LINK || "", "sample-file.xlsx")
              }
            >
              <Icon icon="hugeicons:download-square-02" className="text-v2-content-on-info size-4" />
              <span className="text-body4-medium text-v2-content-on-info">
                {t("product.excelUpload.downloadSampleFile")}
              </span>
            </div>

            <div
              className="flex items-center gap-1 cursor-pointer"
              onClick={() =>
                handleDownloadSampleFile(
                  process.env.NEXT_PUBLIC_SAMPLE_EXCEL_UPLOAD_LINK || "",
                  "sample-upload-file.xlsx"
                )
              }
            >
              <Icon icon="hugeicons:upload-square-02" className="text-v2-content-on-info size-4" />
              <span className="text-body4-medium text-v2-content-on-info">
                {t("product.excelUpload.uploadSampleFile")}
              </span>
            </div>
          </div>

          <Link className="flex items-center gap-1" href="#">
            <Icon icon="hugeicons:download-square-02" className="text-v2-content-secondary size-4" />
            <span className="text-body4-medium text-v2-content-secondary">
              {t("product.excelUpload.educationalVideo")}
            </span>
          </Link>
        </div>

        <div className="mt-4  ">
          <div className="flex flex-col gap-4">
            <ExcelUploader
              value={url}
              onUploaded={file => {
                setUrl(file?.url || "");
              }}
              onRemove={() => setUrl("")}
              onError={error => onErrorAttachUploadedFileToRetailerProfile(error)}
            />

            <InputHelper error>{error}</InputHelper>
          </div>

          {/* <Button
              variant="secondaryGray"
              startAdornment={<Icon icon="hugeicons:upload-square-02" className="text-v2-content-secondary size-4" />}
            >
              {t("product.excelUpload.uploadFile")}
            </Button> */}
        </div>

        <div className="mt-4 flex items-center justify-between">
          <Button variant="secondaryGray" onClick={hideModal}>
            {t("product.excelUpload.cancel")}
          </Button>
          <Button
            onClick={onUpload}
            disabled={!url || isImportLoading}
            endAdornment={isImportLoading && <CircularProgress size={21} />}
          >
            {t("product.excelUpload.importProduct")}
          </Button>
        </div>
      </div>
    </>
  );
}

export default ExcelUploadModal;

import "./editor.css";

import React, { useRef, useEffect, useCallback, useState } from "react";
import { Editor } from "@tinymce/tinymce-react";
import InputHelper from "../CustomFormHelperText/InputHelper";
import { Theme, useMediaQuery, useTheme } from "@mui/material";

interface TinyMCEEditorProps {
  initialValue?: string;
  onChange?: (value: string) => void;
  placeholder?: string;
  autoFocus?: boolean;
  isRTL?: boolean;
  error?: boolean;
  helperText?: React.ReactNode;
}

function isHTMLString(str?: string) {
  if (!str) return false;
  const htmlTagPattern = /<[^>]+>/;
  return htmlTagPattern.test(str);
}

// Loading component
const EditorLoading = ({ isRTL }: { isRTL: boolean }) => (
  <div
    className="tinymce-loading-container"
    style={{
      minHeight: "400px",
      display: "flex",
      alignItems: "center",
      justifyContent: "center",
      backgroundColor: "#f8f9fa",
      border: "1px solid #dee2e6",
      borderRadius: "4px",
      direction: isRTL ? "rtl" : "ltr"
    }}
  >
    <div style={{ textAlign: "center" }}>
      <div
        className="spinner"
        style={{
          width: "40px",
          height: "40px",
          border: "4px solid #f3f3f3",
          borderTop: "4px solid #3498db",
          borderRadius: "50%",
          animation: "spin 1s linear infinite",
          margin: "0 auto 10px"
        }}
      />
    </div>
  </div>
);

export default function TinyMCEEditor({
  autoFocus = false,
  initialValue = "",
  onChange,
  placeholder = "",
  error = false,
  helperText,
  isRTL = true
}: TinyMCEEditorProps) {
  const editorRef = useRef<any>(null);
  const [isLoading, setIsLoading] = useState(true);
  const isMobile = useMediaQuery((theme: Theme) => theme.breakpoints.down(768));

  // Process initial value to ensure proper format
  const processedInitialValue = React.useMemo(() => {
    if (!initialValue) return "<p><br></p>";

    if (isHTMLString(initialValue)) {
      let processedHtml = initialValue.replace(/<p><\/p>/g, "<p><br></p>").trim();
      return processedHtml || "<p><br></p>";
    } else {
      const lines = initialValue.split(/\r?\n/);
      if (lines.length === 1) {
        return `<p>${lines[0] || "<br>"}</p>`;
      } else {
        return lines.map(line => `<p>${line || "<br>"}</p>`).join("");
      }
    }
  }, [initialValue]);

  // Handle content change
  const handleEditorChange = useCallback(
    (content: string) => {
      if (onChange) {
        let processedHtml = content.replace(/<p><\/p>/g, "<p><br></p>").trim();
        if (!processedHtml || processedHtml === "<p></p>") {
          processedHtml = "<p><br></p>";
        }
        onChange(processedHtml);
      }
    },
    [onChange]
  );

  // TinyMCE configuration
  const editorConfig = {
    placeholder: placeholder,
    height: 400,
    min_height: 200,
    max_height: 500,
    directionality: isRTL ? "rtl" : "ltr",
    language: isRTL ? "fa" : "en",
    text_direction: isRTL ? "rtl" : "ltr",
    forced_root_block: "p",
    force_p_newlines: true,
    forced_root_block_attrs: { "data-mce-keep": true },
    keep_styles: false,
    remove_trailing_brs: false,

    base_url: "/tinymce",

    content_style: `
      body {
        font-family: ${isRTL ? "Tahoma, Arial, sans-serif" : "Arial, sans-serif"};
        font-size: 14px;
        direction: ${isRTL ? "rtl" : "ltr"};
        text-align: ${isRTL ? "right" : "left"};
        unicode-bidi: ${isRTL ? "bidi-override" : "normal"};
      }
      p {
        margin: 0 0 10px 0;
        direction: ${isRTL ? "rtl" : "ltr"};
        text-align: ${isRTL ? "right" : "left"};
        unicode-bidi: ${isRTL ? "embed" : "normal"};
      }
      ${
        isRTL
          ? `
        * {
          direction: rtl !important;
        }
        .mce-content-body {
          direction: rtl !important;
          text-align: right !important;
        }
      `
          : ""
      }
    `,

    plugins: [
      "advlist",
      "autolink",
      "lists",
      "link",
      "image",
      "charmap",
      "preview",
      "anchor",
      "searchreplace",
      "visualblocks",
      "code",
      "fullscreen",
      "insertdatetime",
      "media",
      "table",
      "help",
      "wordcount",
      "hr",
      "nonbreaking",
      "textpattern",
      "directionality"
    ],

    toolbar: isMobile
      ? "undo redo | bold italic | alignleft aligncenter alignright | bullist numlist"
      : [
          "undo redo | formatselect | bold italic underline | forecolor backcolor",
          "alignleft aligncenter alignright alignjustify | bullist numlist"
        ].join(" | "),

    // toolbar_mode: isMobile ? 'sliding' : 'wrap',
    toolbar_sticky: true,
    // toolbar_sticky_offset: isMobile ? 0 : 50,

    end_container_on_empty_block: true,
    newline_behavior: "default",
    inline_boundaries_selector: "a[href],code",
    object_resizing: true,
    table_resize_bars: true,
    fix_list_elements: true,
    convert_urls: false,
    paste_data_images: true,
    paste_as_text: false,
    paste_retain_style_properties: "all",
    paste_remove_styles_if_webkit: false,
    code_dialog_height: 400,
    code_dialog_width: 800,
    image_advtab: true,
    image_caption: true,
    image_list: [],
    automatic_uploads: true,
    file_picker_types: "image",

    // Table settings
    table_default_attributes: {
      border: "1"
    },
    table_default_styles: {
      "border-collapse": "collapse"
    },

    // Link settings
    link_default_target: "_blank",
    link_assume_external_targets: true,
    block_formats:
      "Paragraph=p; Heading 1=h1; Heading 2=h2; Heading 3=h3; Heading 4=h4; Heading 5=h5; Heading 6=h6; Preformatted=pre",

    style_formats: [
      {
        title: "Headers",
        items: [
          { title: "Header 1", format: "h1" },
          { title: "Header 2", format: "h2" },
          { title: "Header 3", format: "h3" },
          { title: "Header 4", format: "h4" },
          { title: "Header 5", format: "h5" },
          { title: "Header 6", format: "h6" }
        ]
      },
      {
        title: "Inline",
        items: [
          { title: "Bold", icon: "bold", format: "bold" },
          { title: "Italic", icon: "italic", format: "italic" },
          { title: "Underline", icon: "underline", format: "underline" },
          { title: "Strikethrough", icon: "strikethrough", format: "strikethrough" },
          { title: "Superscript", icon: "superscript", format: "superscript" },
          { title: "Subscript", icon: "subscript", format: "subscript" },
          { title: "Code", icon: "code", format: "code" }
        ]
      },
      {
        title: "Blocks",
        items: [
          { title: "Paragraph", format: "p" },
          { title: "Blockquote", format: "blockquote" },
          { title: "Div", format: "div" },
          { title: "Pre", format: "pre" }
        ]
      },
      {
        title: "Alignment",
        items: [
          { title: "Left", icon: "alignleft", format: "alignleft" },
          { title: "Center", icon: "aligncenter", format: "aligncenter" },
          { title: "Right", icon: "alignright", format: "alignright" },
          { title: "Justify", icon: "alignjustify", format: "alignjustify" }
        ]
      }
    ],

    auto_focus: autoFocus,
    resize: "both",
    branding: false,

    // Mobile-specific settings
    mobile: {
      theme: "silver",
      toolbar_mode: "sliding",
      menubar: false,
      toolbar_sticky: true,
      toolbar_sticky_offset: 0,
      plugins: [
        "advlist",
        "autolink",
        "lists",
        "link",
        "image",
        "charmap",
        "preview",
        "anchor",
        "searchreplace",
        "visualblocks",
        "code",
        "fullscreen",
        "insertdatetime",
        "media",
        "table",
        "help",
        "wordcount",
        "directionality"
      ],
      toolbar: "undo redo | bold italic | alignleft aligncenter alignright | bullist numlist | link image"
    },

    setup: (editor: any) => {
      editor.on("init", () => {
        // Hide loading when editor is fully initialized
        setIsLoading(false);

        const body = editor.getBody();

        // Force toolbar visibility on mobile
        if (isMobile) {
          const toolbar = editor.getContainer().querySelector(".tox-toolbar");
          const editorHeader = editor.getContainer().querySelector(".tox-editor-header");

          if (toolbar) {
            toolbar.style.display = "flex";
            toolbar.style.visibility = "visible";
            toolbar.style.opacity = "1";
          }

          if (editorHeader) {
            editorHeader.style.display = "block";
            editorHeader.style.visibility = "visible";
            editorHeader.style.height = "auto";
          }
        }

        editor.on("focus", (e: any) => {
          e.preventDefault();
        });

        editor.on("keydown", (e: KeyboardEvent) => {
          if (e.key === "Enter" && !e.shiftKey) {
            return true;
          }
          if (e.shiftKey && e.key === "Enter") {
            e.preventDefault();
            editor.insertContent("<br>");
            return false;
          }
        });

        editor.on("input", () => {
          if (isRTL) {
            const body = editor.getBody();
            const paragraphs = body.querySelectorAll("p");
            paragraphs.forEach((p: any) => {
              if (!p.getAttribute("dir")) {
                p.style.direction = "rtl";
                p.style.textAlign = "right";
                p.setAttribute("dir", "rtl");
              }
            });
          }
        });

        if (isRTL) {
          body.style.fontFamily = 'Tahoma, "Iranian Sans", "B Nazanin", Arial, sans-serif';
          body.style.direction = "rtl";
          body.style.textAlign = "right";
          body.style.unicodeBidi = "bidi-override";
          body.setAttribute("dir", "rtl");

          const paragraphs = body.querySelectorAll("p");
          paragraphs.forEach((p: any) => {
            p.style.direction = "rtl";
            p.style.textAlign = "right";
            p.style.unicodeBidi = "embed";
            p.setAttribute("dir", "rtl");
          });
        }
      });

      // Show loading when editor starts initializing
      editor.on("PreInit", () => {
        setIsLoading(true);
      });

      editor.ui.registry.addButton("rtltoggle", {
        text: isRTL ? "EN" : "فا",
        onAction: () => {
          const body = editor.getBody();
          const currentDir = body.dir || body.style.direction;
          const newDir = currentDir === "rtl" ? "ltr" : "rtl";

          body.dir = newDir;
          body.style.direction = newDir;
          body.style.textAlign = newDir === "rtl" ? "right" : "left";

          if (newDir === "rtl") {
            body.style.fontFamily = 'Tahoma, "Iranian Sans", "B Nazanin", Arial, sans-serif';
          } else {
            body.style.fontFamily = "Arial, sans-serif";
          }
        }
      });
    },

    init_instance_callback: (editor: any) => {
      setIsLoading(false);
      if (autoFocus) {
        editor.focus();
      }

      // Additional mobile toolbar fix
      if (isMobile) {
        setTimeout(() => {
          const toolbar = editor.getContainer().querySelector(".tox-toolbar");
          const editorHeader = editor.getContainer().querySelector(".tox-editor-header");

          if (toolbar) {
            toolbar.style.display = "flex";
            toolbar.style.visibility = "visible";
            toolbar.style.opacity = "1";
            toolbar.classList.add("mobile-toolbar-visible");
          }

          if (editorHeader) {
            editorHeader.style.display = "block";
            editorHeader.style.visibility = "visible";
          }
        }, 100);

        // Watch for toolbar visibility changes
        const observer = new MutationObserver(mutations => {
          mutations.forEach(mutation => {
            if (mutation.type === "attributes" && mutation.attributeName === "style") {
              const toolbar = editor.getContainer()?.querySelector(".tox-toolbar");
              if (toolbar && (toolbar.style.display === "none" || toolbar.style.visibility === "hidden")) {
                toolbar.style.display = "flex";
                toolbar.style.visibility = "visible";
                toolbar.style.opacity = "1";
              }
            }
          });
        });

        const editorContainer = editor.getContainer();
        if (editorContainer) {
          observer.observe(editorContainer, {
            attributes: true,
            subtree: true,
            attributeFilter: ["style", "class"]
          });
        }

        /* ---------------- Cleanup observer when editor is destroyed --------------- */
        editor.on("remove", () => {
          try {
            observer.disconnect();
          } catch (error) {
            console.warn("MutationObserver cleanup error:", error);
          }
        });
      }
    }
  };

  const containerStyles = {
    direction: isRTL ? ("rtl" as const) : ("ltr" as const),
    textAlign: isRTL ? ("right" as const) : ("left" as const)
  };

  const editorClassName = `tinymce-editor-container ${error ? "tinymce-error" : ""}`;

  return (
    <>
      <div className={editorClassName} style={containerStyles}>
        {isLoading && <EditorLoading isRTL={isRTL} />}

        <div style={{ display: isLoading ? "none" : "block" }}>
          <Editor
            tinymceScriptSrc="/tinymce/tinymce.min.js"
            ref={editorRef}
            initialValue={processedInitialValue}
            init={editorConfig}
            onEditorChange={handleEditorChange}
          />
        </div>
      </div>

      {helperText && (
        <InputHelper error={error} className="mt-1">
          {helperText as string}
        </InputHelper>
      )}
    </>
  );
}

import useModal from "@/utils/hooks/useModal";
import { Box, Grid, IconButton, Modal, Theme, useMediaQuery } from "@mui/material";
import React, { useEffect, useMemo, useState, useRef, useCallback } from "react";
import Close from "@mui/icons-material/Close";
import Image from "next/image";
import clsx from "clsx";
import { Drawer } from "vaul";
import { twMerge } from "tailwind-merge";
import useModalStore from "@/store/zustand/modalStore";
import Button from "@/components/ui/Button";

function ModalProvider() {
  const modalState = useModalStore();
  const {
    isOpen,
    modalProps = {},
    actions = [],
    body,
    title,
    subTitle,
    icon,
    width = 428,
    closable = true
  } = modalState;

  const { className, containerClassName, showCloseIcon = true } = modalProps;
  const isMobile = useMediaQuery((theme: Theme) => theme.breakpoints.down(768));
  const [isDrawerOpen, setIsDrawerOpen] = useState(false);

  const { hideModal } = useModal();
  const isInitializingRef = useRef(false);

  useEffect(() => {
    if (isMobile) {
      if (isOpen && !isInitializingRef.current) {
        isInitializingRef.current = true;
        setTimeout(() => {
          setIsDrawerOpen(true);
          isInitializingRef.current = false;
        }, 50);
      } else if (!isOpen) {
        setIsDrawerOpen(false);
      }
    }
  }, [isOpen, isMobile]);

  const handleDrawerClose = useCallback(() => {
    if (closable) {
      setIsDrawerOpen(false);
      setTimeout(() => {
        hideModal();
      }, 150);
    }
  }, [closable, hideModal]);

  const handleBackdropClick = useCallback(() => {
    if (closable) {
      handleDrawerClose();
    }
  }, [closable, handleDrawerClose]);

  if (isMobile) {
    return (
      <Drawer.Root
        open={isDrawerOpen}
        onOpenChange={open => {
          if (!open) {
            handleDrawerClose();
          }
        }}
        shouldScaleBackground
      >
        <Drawer.Portal>
          <Drawer.Overlay className="fixed inset-0 bg-[black] bg-opacity-60 z-50" onClick={handleBackdropClick} />
          <Drawer.Content
            className="bg-cards flex flex-col rounded-t-2xl h-fit mt-24 max-h-[96%] fixed bottom-0 left-0 right-0 z-[999999999]"
            style={{
              backdropFilter: "blur(20px)",
              WebkitBackdropFilter: "blur(20px)"
            }}
          >
            <div className="mx-auto w-9 h-[5px] flex-shrink-0 rounded-full bg-gray-400 mb-2 mt-3" />

            <div className={twMerge("px-6 pb-6 w-full h-full overflow-auto", containerClassName)}>
              {closable && showCloseIcon && (
                <>
                  {icon ? (
                    <div className="flex justify-center">
                      {typeof icon === "string" ? (
                        <Image src={icon} alt="modal-icon" width={48} height={48} className="size-12" />
                      ) : (
                        icon
                      )}
                    </div>
                  ) : (
                    <div className="flex justify-end">
                      <IconButton onClick={handleDrawerClose} className="p-1" size="small">
                        <Close className="size-5" />
                      </IconButton>
                    </div>
                  )}
                </>
              )}

              {title && <h5 className="text-base font-bold text-center text-v2-content-primary mt-4">{title}</h5>}

              {subTitle && (
                <h6 className="text-xs font-medium text-center text-v2-content-tertiary mt-2">{subTitle}</h6>
              )}

              {body && <>{typeof body === "function" ? body() : body}</>}

              {!!actions?.length && (
                <Grid container spacing={1} className="mt-6">
                  {actions?.reverse()?.map(({ label, onClick, ...restProps }, index) => (
                    <Grid key={index} item xs={12} md={!!actions?.length ? 6 : 12}>
                      <Button
                        onClick={onClick}
                        {...restProps}
                        size={restProps?.size || "xl"}
                        className={twMerge("w-full", restProps?.className)}
                      >
                        {label}
                      </Button>
                    </Grid>
                  ))}
                </Grid>
              )}
            </div>
          </Drawer.Content>
        </Drawer.Portal>
      </Drawer.Root>
    );
  }

  if (!isOpen) return null;

  return (
    <Modal open={isOpen} onClose={closable ? hideModal : undefined} className={clsx(className, "z-[999999999]")}>
      <Box
        width={width}
        className={twMerge(
          "absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 rounded-lg p-6 bg-cards border-none outline-none",
          containerClassName
        )}
      >
        {closable && showCloseIcon && (
          <IconButton onClick={closable ? hideModal : undefined} className="ms-auto block p-0 size-5">
            <Close className="size-5" />
          </IconButton>
        )}
        {icon && (
          <>
            {typeof icon === "string" ? (
              <Image src={icon} alt="modal-icon" width={48} height={48} className="size-12 block me-auto" />
            ) : (
              icon
            )}
          </>
        )}

        {title && <h5 className="text-base font-bold text-v2-content-primary text-start mt-5">{title}</h5>}
        {subTitle && <h6 className="text-start text-v2-content-tertiary text-[13px] font-medium mt-1.5">{subTitle}</h6>}
        {body && typeof body === "function" ? body() : body}

        {!!actions?.length && (
          <div className="mt-4 flex gap-2">
            {actions?.map(({ label, onClick, ...restProps }, index) => (
              <div key={index} className={twMerge(!!actions?.length ? "w-1/2" : "w-full")}>
                <Button
                  onClick={onClick}
                  {...restProps}
                  size={restProps?.size || "xl"}
                  className={twMerge("w-full", restProps?.className)}
                >
                  {label}
                </Button>
              </div>
            ))}
          </div>
        )}
      </Box>
    </Modal>
  );
}

export default ModalProvider;

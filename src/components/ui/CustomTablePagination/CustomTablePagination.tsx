import "./CustomTablePagination.css";

import { Box } from "@mui/material";
import { IconButton } from "@mui/material";
import { useTheme } from "@mui/material/styles";

import React from "react";
import KeyboardArrowRight from "@mui/icons-material/KeyboardArrowRight";
import { MenuItem } from "@mui/material";
import { Select } from "@mui/material";
import { Typography } from "@mui/material";
import { SelectChangeEvent } from "@mui/material";
import KeyboardArrowLeft from "@mui/icons-material/KeyboardArrowLeft";
import KeyboardArrowDownIcon from "@mui/icons-material/KeyboardArrowDown";

interface CustomTablePaginationProps {
  count: number;
  page: number;
  rowsPerPage: number;
  onPageChange: (event: React.MouseEvent<HTMLButtonElement>, newPage: number) => void;
  rowsPerPageOptions: Array<number>;
  onRowsPerPageChange: (event: SelectChangeEvent<number>) => void;
  labelRowsPerPage?: string;
}

function CustomTablePagination(props: CustomTablePaginationProps) {
  const theme = useTheme();
  const { rowsPerPageOptions, count, rowsPerPage, page, onPageChange, onRowsPerPageChange, labelRowsPerPage } = props;

  const handleBackButtonClick = (event: any) => {
    onPageChange(event, page - 1);
  };

  const handleNextButtonClick = (event: any) => {
    onPageChange(event, page + 1);
  };

  const totalPages = count <= rowsPerPage ? 1 : Math.ceil(count / rowsPerPage);

  const renderPageNumbers = () => {
    const pageNumbers = [];

    if (totalPages <= 7) {
      for (let i = 1; i <= totalPages; i++) {
        pageNumbers.push(i);
      }
    } else {
      pageNumbers.push(1);
      if (page > 4) pageNumbers.push("...");
      const startPage = Math.max(2, page - 2);
      const endPage = Math.min(totalPages - 1, page + 2);
      for (let i = startPage; i <= endPage; i++) {
        pageNumbers.push(i);
      }
      if (page < totalPages - 3) pageNumbers.push("...");
      pageNumbers.push(totalPages);
    }

    return pageNumbers.map((pageNumber, index) => (
      <Box
        key={index}
        className={`sx-pagination-page ${pageNumber === page ? "sx-pagination-page-active" : ""}`}
        onClick={
          pageNumber !== "..."
            ? event => onPageChange(event as unknown as React.MouseEvent<HTMLButtonElement>, pageNumber as number)
            : undefined
        }
      >
        {pageNumber !== "..." ? pageNumber : pageNumber}
      </Box>
    ));
  };

  if (count && count < 10) {
    return null;
  }

  return (
    <div className="flex items-center justify-center flex-wrap relative">
      {Boolean(rowsPerPageOptions.length) && (
        // as we wanted to centralize pagination so made the below div absolute
        <div className="flex items-center">
          <Typography whiteSpace="nowrap">{labelRowsPerPage}</Typography>
          <Select
            value={rowsPerPage}
            onChange={v => onRowsPerPageChange?.(v)}
            classes={{
              root: "sx-customtablepagination-4561"
            }}
            endAdornment={<KeyboardArrowDownIcon className="text-gray-600" />}
          >
            {rowsPerPageOptions.map(option => (
              <MenuItem key={option} value={option}>
                {option}
              </MenuItem>
            ))}
          </Select>
        </div>
      )}
      <div className="flex items-center justify-end md:justify-center  ">
        <IconButton onClick={handleBackButtonClick} disabled={page === 1} aria-label="previous page">
          {theme.direction === "rtl" ? <KeyboardArrowRight /> : <KeyboardArrowLeft />}
        </IconButton>
        {renderPageNumbers()}
        <IconButton onClick={handleNextButtonClick} disabled={page >= totalPages} aria-label="next page">
          {theme.direction === "rtl" ? <KeyboardArrowLeft /> : <KeyboardArrowRight />}
        </IconButton>
      </div>
    </div>
  );
}

export default React.memo(CustomTablePagination);

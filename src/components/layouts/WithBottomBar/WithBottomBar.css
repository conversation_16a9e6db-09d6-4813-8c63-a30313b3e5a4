.bottom-navigation {
  display: none;
}

.bottom-navigation-action {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start;
  padding-top: 20px;
  flex: 1;
  text-decoration: none;
  color: inherit;
  min-width: 0;
  transition: all 0.2s ease-in-out;
  cursor: pointer;
  /* iOS Safari touch fixes */
  -webkit-tap-highlight-color: transparent;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  user-select: none;
  touch-action: manipulation;
  /* Ensure proper touch target size for iOS */
  min-height: 44px;
  position: relative;
}

.bottom-navigation-action:hover {
  opacity: 0.8;
}

/* iOS Safari active state fix */
.bottom-navigation-action:active {
  opacity: 0.6;
}

.bottom-navigation-action-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  /* Ensure content doesn't interfere with touch events */
  pointer-events: none;
  width: 100%;
}

.bottom-navigation-action-label {
  font-size: 11px;
  font-weight: 500;
  color: rgb(var(--color-gray-400));
  margin-top: 2px;
  text-align: center;
}

.bottom-navigation-action-active .bottom-navigation-action-label {
  color: rgb(var(--color-purple-500));
}

.bottom-navigation-action-active svg {
  color: rgb(var(--color-purple-500));
}

@media (max-width: 370px) {
  .bottom-navigation-action {
    margin-inline: -8px;
  }
}

@media screen and (max-width: 768px) {
  .bottom-navigation + #sx-layout-108 {
    margin-block-end: 56px;
  }

  .bottom-navigation {
    display: flex;
    background-color: rgb(var(--color-cards));
    box-shadow: 0px -12px 32px 0px rgba(87, 111, 133, 0.07);
    z-index: 40;
    position: fixed;
    bottom: 0;
    height: 93px;
    width: 100%;
    left: 0;
    right: 0;
    align-items: flex-start;
    justify-content: space-around;
  }

  #sx-customizer-6749 {
    inset-block-end: 70px !important;
  }
}

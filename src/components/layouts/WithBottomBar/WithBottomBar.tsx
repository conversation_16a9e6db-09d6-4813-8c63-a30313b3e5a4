import React, { ComponentType } from "react";
import { menuitems, MenuitemsType } from "@/components/containers/sidebar/MenuItems";
import { USERTYPES } from "@/constants/userTypes";
import useRoleBasePath from "@/utils/hooks/useRoleBasePath";
import { Icon } from "@iconify/react";
import clsx from "clsx";
import { usePathname, useRouter } from "next/navigation";

import "./WithBottomBar.css";
import useSessionStore from "@/store/zustand/sessionStore";
import Link from "next/link";

interface WithBottomBarProps {
  [x: string]: any;
}

function WithBottomBar<P extends WithBottomBarProps>(WrappedComponent: ComponentType<P>): ComponentType<P> {
  function HocComponent(props: P) {
    const { user_type } = useSessionStore();
    const pathname = usePathname();
    const router = useRouter();
    const makePath = useRoleBasePath();

    const Menuitems = menuitems({ makePath })?.filter(item => item?.for?.includes(user_type as USERTYPES));

    const isActive = (item: MenuitemsType) => {
      // If we're on exactly the home page and this is the home item
      if (item.name === "home" && item.href.includes(pathname)) {
        return true;
      }

      // Check if current path matches the item's href directly
      if (item.href && pathname === item.href) {
        return true;
      }

      // Check if current path includes the item's name (if name is provided and not empty)
      if (item.name && item.name !== "" && pathname.includes(item.name)) {
        return true;
      }

      // Check extraIsActives array if it exists
      if (Array.isArray(item.extraIsActives) && item.extraIsActives.length > 0) {
        return item.extraIsActives.some(path => pathname.includes(path));
      }

      return false;
    };

    // Handle click for iOS compatibility
    const handleNavClick = (e: React.MouseEvent | React.TouchEvent, href: string) => {
      e.preventDefault();
      e.stopPropagation();

      // Add a small delay to ensure touch events complete
      setTimeout(() => {
        router.push(href);
      }, 10);
    };

    if (!Menuitems?.length) return null;

    return (
      <>
        <div className="pb-[93px] mb-8 md:pb-0 md:mb-0 h-full">
          <WrappedComponent {...props} />
        </div>

        <nav className="bottom-navigation" data-value="Recents">
          {Menuitems.filter(item => item.isMobile === undefined || item.isMobile).map(item => (
            <Link
              key={item.id}
              href={item.href}
              prefetch
              className={clsx("bottom-navigation-action", isActive(item) && "bottom-navigation-action-active")}
              onClick={e => handleNavClick(e, item.href)}
              onTouchEnd={e => handleNavClick(e, item.href)}
              style={{
                WebkitTapHighlightColor: "transparent",
                WebkitTouchCallout: "none",
                WebkitUserSelect: "none",
                touchAction: "manipulation"
              }}
              role="button"
              tabIndex={0}
            >
              <div className="bottom-navigation-action-content">
                <Icon
                  icon={(isActive(item) ? item.selectedIcon : item.icon) || ""}
                  color={isActive(item) ? "rgb(var(--color-purple-500))" : "rgb(var(--color-gray-400))"}
                  width={24}
                  height={24}
                />
                <span className="bottom-navigation-action-label">{item?.title}</span>
              </div>
            </Link>
          ))}
        </nav>
      </>
    );
  }

  return HocComponent;
}

export default WithBottomBar;

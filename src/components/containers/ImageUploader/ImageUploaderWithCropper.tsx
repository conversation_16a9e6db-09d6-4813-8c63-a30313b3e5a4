import React from "react";
import { TImageUploader<PERSON>ithCropper } from "./types";
import ImageUploader from "./ImageUploader";
import useModal from "@/utils/hooks/useModal";
import ImageCropper from "@/components/ui/ImageCropper/ImageCropper";
import { useMediaQuery } from "@mui/system";
import { Theme } from "@mui/material";

/**
 * Uses ImageUploader and open modal before upload process
 * Just responsible for uploading file
 * Dose NOT have UI! the ui should passed in the children
 */
function ImageUploaderWithCropper({ children, cropperProps, ...restProps }: TImageUploaderWithCropper) {
  const { showModal, hideModal } = useModal();
  const isMobile = useMediaQuery((theme: Theme) => theme.breakpoints.down(768));

  const onBeforeUpload = async (file: File) => {
    return new Promise<File>((resolve, reject) => {
      const onConfirm = (file: File) => {
        resolve(file);
        hideModal();
      };
      const onCancel = () => {
        reject();
        hideModal();
      };

      showModal({
        width: 585,
        modalProps: {
          showCloseIcon: false
        },
        closable: false,
        body: () => {
          if (isMobile) return null;

          return (
            <ImageCropper
              cropperProps={cropperProps}
              file={file}
              onConfirm={onConfirm}
              onCancel={onCancel}
              maxFileSizeMB={restProps?.maxFileSizeMB}
              withCompressorMaxFileSizeMB={restProps?.withCompressorMaxFileSizeMB}
            />
          );
        }
      });
    });
  };

  return (
    <ImageUploader onBeforeUpload={onBeforeUpload} {...restProps}>
      {children}
    </ImageUploader>
  );
}

export default ImageUploaderWithCropper;

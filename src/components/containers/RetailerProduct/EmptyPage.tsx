import Image from "next/image";
import React from "react";
import { useTranslation } from "react-i18next";

function EmptyPage({ imageWidth = 287 }: { imageWidth?: number }) {
  const { t } = useTranslation();

  return (
    <div className="flex items-center justify-center h-full w-full">
      <div className="flex flex-col items-center gap-2">
        <Image src="/images/product-list-empty-2.svg" width={imageWidth} alt="empty list palceholder" height={225} />
        <div className="mt-7 text-gray-999 text-lg font-bold">{t("product.noProductFound")}</div>
        <div className="text-gray-500 text-sm font-medium">{t("product.noProductFoundDescription")}</div>
      </div>
    </div>
  );
}

export default EmptyPage;

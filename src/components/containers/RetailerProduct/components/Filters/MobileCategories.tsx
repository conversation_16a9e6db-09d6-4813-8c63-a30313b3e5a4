import { useGetMetaCategoriesQuery } from "@/store/apps/meta";
import { Icon } from "@iconify/react";
import { CircularProgress } from "@mui/material";
import Image from "next/image";
import React, { ReactNode, useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { twMerge } from "tailwind-merge";
import { TProductFilters } from "./useProductFilters";

function RootMenuItem({
  id,
  children,
  icon,
  hasChildren,
  isActive,
  onClick,
  onClickMore
}: {
  id: string;
  children: ReactNode;
  icon?: string;
  onClick?: (id: string) => void;
  onClickMore?: (id: string) => void;
  hasChildren?: boolean;
  isActive?: boolean;
}) {
  return (
    <div
      className={twMerge(
        "p-4 text-gray-999 text-sm font-medium hover:bg-purple-50 cursor-pointer rounded-md flex items-center justify-between gap-1.5",
        isActive ? "bg-purple-50" : ""
      )}
      onClick={() => onClick?.(id)}
    >
      <div className="shrink-0 flex-1 flex items-center gap-1.5">
        {icon && (
          <div className="shrink-0 relative h-[18px] w-[18px]">
            <Image src={icon} fill alt={icon} />
          </div>
        )}
        <div className="shrink-0">{children}</div>
      </div>
      {hasChildren && (
        <Icon
          icon="solar:alt-arrow-left-outline"
          width={18}
          height={18}
          className="text-gray-400"
          onClick={e => {
            e.preventDefault();
            e.stopPropagation();
            onClickMore?.(id);
          }}
        />
      )}
    </div>
  );
}

function MenuItem({
  id,
  children,
  onClick,
  isActive
}: {
  id: string;
  children: ReactNode;
  onClick: (id: string) => void;
  isActive?: boolean;
}) {
  return (
    <div
      className={twMerge(
        "text-xs font-medium text-gray-600 cursor-pointer hover:text-purple-500 p-4",
        isActive ? "text-purple-500 cursor-default" : ""
      )}
      onClick={() => (!isActive ? onClick(id) : undefined)}
    >
      {children}
    </div>
  );
}

function MobileCategoriesActionSheet({
  closePopOver,
  setFilterState,
  defaultValue,
}: {
  closePopOver: () => void;
  setFilterState: (k: keyof TProductFilters, v: any) => void;
  defaultValue?: string;
}) {
  const { t } = useTranslation();
  const { data, isLoading } = useGetMetaCategoriesQuery();
  const rootCategories = data?.data?.filter(a => a.parentId === "00000000-0000-0000-0000-000000000000");
  const [selectedRootCategoryId, setSelectedRootCategoryId] = useState<string | undefined>(
    rootCategories?.find(a => a.subCategories?.some(b => b.id === defaultValue))?.id || undefined
  );
  const selectedRootCategory = rootCategories?.find(a => a.id === selectedRootCategoryId);

  if (isLoading) {
    return (
      <div className="items-center justify-center flex w-[90vw] xl:w-[50vw] px-16 py-56">
        <CircularProgress />
      </div>
    );
  }

  const handleSelectCategory = (id: string) => {
    closePopOver();
    setFilterState("category", id);
  };

  const handleClickRootItemMore = (id: string) => {
    setSelectedRootCategoryId(id);
  };

  const onClickBack = () => {
    setSelectedRootCategoryId(undefined);
  };

  if (selectedRootCategory?.subCategories && selectedRootCategory?.subCategories?.length > 0) {
    return (
      <>
        <div className="p-2.5 text-gray-600 text-sm font-normal flex items-center gap-1.5" onClick={onClickBack}>
          <Icon icon="solar:arrow-right-outline" width={20} height={20} />
          {t("retailerProduct.filters.back")}
        </div>

        <div className="flex-1 flex flex-col gap-2 pt-2.5 flex-wrap max-h-[80vh]">
          {selectedRootCategory?.subCategories?.map(item => (
            <MenuItem
              key={item?.id}
              id={item?.id}
              onClick={id => handleSelectCategory(id)}
              isActive={item?.id === selectedRootCategoryId || item?.id === defaultValue}
            >
              {item?.name}
            </MenuItem>
          ))}
        </div>
      </>
    );
  }

  return (
    <>
      <div className="p-2.5 text-gray-600 text-sm font-normal flex items-center gap-1.5 mb-1.5" onClick={onClickBack}>
        {t("retailerProduct.filters.selectCategory")}
      </div>

      <div className="flex flex-col gap-1 w-full max-h-[80vh] overflow-x-hidden overflow-y-auto">
        {rootCategories?.map(item => (
          <RootMenuItem
            key={item?.id}
            id={item?.id}
            icon={item?.icon}
            isActive={item?.id === selectedRootCategoryId}
            onClick={id => handleSelectCategory(id)}
            onClickMore={id => handleClickRootItemMore(id)}
            hasChildren={item?.subCategories?.length > 0}
          >
            {item?.name}
          </RootMenuItem>
        ))}
      </div>
    </>
  );
}

export default MobileCategoriesActionSheet;

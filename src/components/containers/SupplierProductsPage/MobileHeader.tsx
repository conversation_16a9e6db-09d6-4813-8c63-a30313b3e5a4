import CustomButton from "@/components/ui/CustomButton/CustomButton";
import { Icon } from "@iconify/react";
import { Avatar, CircularProgress } from "@mui/material";
import Image from "next/image";
import React from "react";
import { THeader } from "./Header";
import { useTranslation } from "react-i18next";
import { useConversationStart } from "@/utils/hooks/useConversationStart";
import Button from "@/components/ui/Button";
import { useOpenProductReturnModal } from "./components/productReturnalModal";

function MobileHeader({ id, cover, logo, name, productName, isSupplierView, bio, returnPolicy }: THeader) {
  const { t } = useTranslation();
  const { onChatStart, isConversationLoading } = useConversationStart();
  const { handleClickReturnalModal } = useOpenProductReturnModal();

  const bindPerformanceScore = {
    0: t("retailerProduct.performanceScore.mid")
  };

  return (
    <div className="flex flex-col gap-2 ">
      <div className="relative w-full h-[162px] bg-[#F4F6FB] rounded-[10px] overflow-hidden">
        {cover ? (
          <Image fill src={cover} alt="cover" className="object-cover object-center" />
        ) : (
          <Image src="/images/placeholders/supplier-cover.svg" fill alt="cover" className="py-8" />
        )}
      </div>

      {/* ------------------------ avatar & name & subtitle ------------------------ */}
      <div className="bg-cards rounded-lg flex flex-col gap-2 p-3">
        <div className="flex items-start justify-between">
          <div className="flex items-center gap-2">
            <Avatar src={logo} className="size-[60px] rounded-full shrink-0" />

            <div className="flex flex-col items-start gap-1.5 flex-1">
              <div className="flex items-center gap-1">
                <div className="text-gray-999 font-medium text-lg">{name}</div>
                {isSupplierView && <Icon icon="bitcoin-icons:verify-filled" className="text-purple-500 size-6" />}
              </div>

              {/* <div className="flex items-center gap-1.5 text-gray-400 font-normal text-xs">لرم ایپسوم متن ساختگی</div> */}
            </div>
          </div>
          {/* <div className="bg-purple-50/90 rounded-lg p-1">
          <div className="flex items-center gap-1 text-purple-500 cursor-pointer">
            <Image src="/images/svgs/redo.svg" alt="returnPolicy" width={18} height={18} />
          </div>
        </div> */}
        </div>

        {/* ---------------------------------- boxes --------------------------------- */}
        <div className="py-1.5 flex gap-4 items-center shrink-0 justify-around">
          {/* ---------------------------------- box 1 --------------------------------- */}
          <div className="flex items-center gap-2 flex-col justify-between">
            <div className="text-purple-500 font-bold text-base flex items-center gap-0.5 mb-1.5">
              {/* <Icon icon="solar:star-bold" width={14} height={14} /> */}
              <Icon icon="solar:star-bold" className="text-purple-500 size-4" />

              <div className="-mb-1.5">۰</div>
            </div>
            <div className="text-v2-content-tertiary text-xs font-medium">{t("retailerProduct.shopScore")}</div>
          </div>

          <div className="h-6 w-px shrink-0 bg-gray-50" />

          {/* ---------------------------------- box 2 --------------------------------- */}
          <div className="flex items-center gap-2 flex-col justify-between">
            <div className="text-gray-999 font-bold text-base">۰</div>
            <div className="text-v2-content-tertiary text-xs font-medium">{t("retailerProduct.followers")}</div>
          </div>

          <div className="h-6 w-px shrink-0 bg-gray-50" />

          {/* ---------------------------------- box 3 --------------------------------- */}
          <div className="flex items-center gap-2 flex-col justify-between">
            <div className="text-gray-999 font-bold text-base">-</div>
            <div className="text-v2-content-tertiary text-xs font-medium">
              {t("retailerProduct.shopPerformanceScore")}
            </div>
          </div>
        </div>

        <div className="flex flex-col gap-4">
          <span className="text-body3-medium">{bio}</span>
          <Button
            variant="destructiveSecondaryGray"
            className="text-v2-content-secondary"
            startAdornment={<Icon icon="solar:undo-right-round-square-linear" className="size-5" />}
            onClick={() => handleClickReturnalModal({ returnPolicy })}
          >
            {t("retailerProduct.supplierReturnPolicy")}
          </Button>
        </div>

        {/* --------------------------------- buttons -------------------------------- */}
        {!isSupplierView && (
          <div className="flex gap-2 items-center">
            <CustomButton
              size="small"
              color="secondary"
              className="flex gap-1.5 flex-1 !text-sm !text-gray-400"
              onClick={() => onChatStart({ content: t("chats.product", { name: productName }), partnerId: id })}
              disabled={isConversationLoading}
            >
              {isConversationLoading ? (
                <CircularProgress size={16} />
              ) : (
                <Icon icon="solar:chat-line-linear" width={18} height={18} />
              )}
              <div>{t("retailerProduct.connectWithSupplier")}</div>
            </CustomButton>
            <CustomButton size="small" color="secondary" className="flex gap-1.5 flex-1 !text-sm !text-purple-500">
              <Icon icon="mage:bookmark-plus" width={18} height={18} />
              <div>{t("retailerProduct.followSupplier")}</div>
            </CustomButton>
          </div>
        )}
      </div>
    </div>
  );
}

export default MobileHeader;

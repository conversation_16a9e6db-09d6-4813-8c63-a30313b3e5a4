import { TSupplierReturnPayload } from "@/store/apps/supplier/types";
import useModal from "@/utils/hooks/useModal";
import Image from "next/image";
import React from "react";
import { useTranslation } from "react-i18next";

function ProductReturnalModal({ returnPolicy }: { returnPolicy?: TSupplierReturnPayload }) {
  const { t } = useTranslation();

  if (!returnPolicy?.isAllowed) {
    return (
      <>
        <div className="flex flex-col items-center justify-center gap-4">
          <div className="size-[75px] rounded-full bg-gray-20 flex items-center justify-center">
            <Image src="/images/svgs/UndoRightRound.svg" width={44} height={44} alt="empty list palceholder" />
          </div>
          <div className="text-lg font-bold">{t("retailerProduct.supplierReturnPolicy")}</div>
        </div>

        <div className="flex flex-col gap-1 py-12 mt-[26px] items-center justify-center bg-gray-20 rounded-lg">
          <div className="text-base font-bold">{t("retailerProduct.doenNotHaveReturnPolicy")}</div>
          <div className="text-sm font-medium text-gray-600">{t("retailerProduct.doenNotHaveReturnPolicyDesc")}</div>
        </div>
      </>
    );
  }

  return (
    <>
      <div className="flex flex-col items-center justify-center gap-4">
        <div className="size-[75px] rounded-full bg-gray-20 flex items-center justify-center">
          <Image src="/images/svgs/UndoRightRound.svg" width={44} height={44} alt="empty list palceholder" />
        </div>
        <div className="text-lg font-bold">{t("retailerProduct.supplierReturnPolicy")}</div>
      </div>

      <div className="p-6 flex flex-col divide-y divide-gray-50">
        <div className="flex flex-col gap-2">
          <div className="text-sm font-medium text-gray-500">{t("retailerProduct.shipingPriceBy")}</div>
          <div className="text-[15px] font-medium text-gray-999">
            {returnPolicy?.shippingPayer === "Supplier" ? t("retailerProduct.supplier") : t("retailerProduct.customer")}
          </div>
        </div>
        <div className="flex flex-col gap-2 mt-4 pt-4">
          <div className="text-sm font-medium text-gray-500">{t("retailerProduct.shipingWithinTime")}</div>
          <div className="text-[15px] font-medium text-gray-999">
            {t("retailerProduct.afterRecieveProduct", { days: returnPolicy?.windowTime?.max })}
          </div>
        </div>
        <div className="flex flex-col gap-2 mt-4 pt-4">
          <div className="text-sm font-medium text-gray-500">{t("retailerProduct.shipingDesc")}</div>
          <div className="text-[15px] font-medium text-gray-999">{returnPolicy?.description}</div>
        </div>
      </div>
    </>
  );
}

export default ProductReturnalModal;

export function useOpenProductReturnModal() {
  const { showModal } = useModal();

  const handleClickReturnalModal = ({ returnPolicy }: { returnPolicy?: TSupplierReturnPayload }) => {
    showModal({ body: <ProductReturnalModal returnPolicy={returnPolicy} />, width: 612 });
  };

  return { handleClickReturnalModal };
}

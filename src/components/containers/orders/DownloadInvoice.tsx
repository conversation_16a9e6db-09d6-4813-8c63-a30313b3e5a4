import CustomButton from "@/components/ui/CustomButton/CustomButton";
import { useGetDownloadInvoiceMutation } from "@/store/apps/order";
import { clientDefaultErrorHandler } from "@/utils/services/utils";
import { Icon } from "@iconify/react";
import { Button, CircularProgress, Divider, Menu } from "@mui/material";
import { useState } from "react";
import { useTranslation } from "react-i18next";

interface IDownloadInvoice {
  orderNumber?: string;
  orderId?: string;
  printCustomerTitle: string;
  printSellerTitle: string;
}

function DownloadInvoice({ orderId, orderNumber, printCustomerTitle, printSellerTitle }: IDownloadInvoice) {
  const { t } = useTranslation();
  const [downloadInvoice, { isLoading: isDownloadingInvoice }] = useGetDownloadInvoiceMutation();

  const [anchorEl, setAnchorEl] = useState(null);
  const handleClick2 = (event: any) => {
    setAnchorEl(event.currentTarget);
  };
  const handleClose = () => {
    setAnchorEl(null);
  };

  const onDownloadSellerInvoice = async (type?: "customer") => {
    if (!orderId) return;

    handleClose();

    try {
      const response = await downloadInvoice({ id: orderId, type });

      if ((response as any)?.error) {
        clientDefaultErrorHandler({ error: (response as any)?.error });
      }

      if ("data" in response && response.data) {
        const pdfBlob = new Blob([response.data], { type: "application/pdf" });

        const url = window.URL.createObjectURL(pdfBlob);

        const tempLink = document.createElement("a");
        tempLink.href = url;
        tempLink.setAttribute("download", `${orderNumber}.pdf`);

        document.body.appendChild(tempLink);
        tempLink.click();

         /* ---- Use setTimeout to ensure the download has started before cleanup ---- */
        setTimeout(() => {
          try {
            if (document.body.contains(tempLink)) {
              document.body.removeChild(tempLink);
            }
            window.URL.revokeObjectURL(url);
          } catch (error) {
            console.warn("Download cleanup error:", error);
          }
        }, 100);
      }
    } catch (err: any) {
      clientDefaultErrorHandler({ error: err });
    }
  };

  if (isDownloadingInvoice) {
    return <CircularProgress size={16} color="inherit" />;
  }

  return (
    <>
      <Button
        size="large"
        aria-label="download factor"
        color="inherit"
        aria-haspopup="true"
        className="sx-profile-7298"
        id="print-factor"
        sx={{
          ...(typeof anchorEl === "object" && {
            color: "primary.main"
          })
        }}
        onClick={handleClick2}
      >
        {isDownloadingInvoice ? (
          <div className="flex items-center justify-center">
            <CircularProgress size={20} color="inherit" />
          </div>
        ) : (
          <div className="flex items-center gap-1">
            <span className="text-body4-medium text-purple-500">{t("downloadInvoice")}</span>
            <Icon icon="solar:alt-arrow-down-outline" className="size-4 text-purple-500" />
          </div>
        )}
      </Button>
      {/* ------------------------------------------- */}
      {/* Message Dropdown */}
      {/* ------------------------------------------- */}
      <Menu
        id="print-factor"
        className="!border-transparent"
        classes={{
          paper: "!rounded-lg !border-transparent shadow-[0px 0px 8px 0px rgba(0, 0, 0, 0.08)]"
        }}
        anchorEl={anchorEl}
        keepMounted
        open={Boolean(anchorEl)}
        onClose={handleClose}
      >
        <div
          onClick={() => onDownloadSellerInvoice("customer")}
          className="flex items-center cursor-pointer gap-1.5 px-5 py-2.5 text-purple-500"
        >
          <span className="text-body4-medium text-v2-content-primary">{printCustomerTitle}</span>
        </div>
        <Divider />
        <div
          onClick={() => onDownloadSellerInvoice()}
          className="flex items-center cursor-pointer gap-1.5 px-5 py-2.5 text-purple-500"
        >
          <span className="text-body4-medium text-v2-content-primary">{printSellerTitle}</span>
        </div>
      </Menu>
    </>
  );
}

export default DownloadInvoice;

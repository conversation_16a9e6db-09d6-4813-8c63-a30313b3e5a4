import Button from "@/components/ui/Button";
import CustomCardContent from "@/components/ui/CustomCard/CustomCard";
import Input from "@/components/ui/inputs/Input";
import useModal from "@/utils/hooks/useModal";
import { Icon } from "@iconify/react";
import { useTranslation } from "react-i18next";
import SelectProductModal, { SelectedProductItem } from "./SelectProductModal";
import { useRouter } from "next/navigation";
import { useState } from "react";
import Image from "next/image";
import { IconPencil, IconPlus } from "@tabler/icons-react";
import AddressModal, { TCustomerFormData } from "@/components/containers/manualOrderPage/AddressModal";
import useCurrency from "@/utils/hooks/useCurrency";

export default function ManualOrderPage() {
  const { t } = useTranslation();
  const { showModal } = useModal();
  const router = useRouter();
  const [selectedProducts, setSelectedProducts] = useState<SelectedProductItem[]>([]);
  const [customerDetails, setCustomerDetails] = useState<TCustomerFormData>();
  const [curr] = useCurrency();
  const { render: renderPrice } = curr ?? { render: v => v };

  console.log(selectedProducts);

  const handleAddProduct = (selectedItems: SelectedProductItem[]) => {
    setSelectedProducts(selectedItems);
  };
  const handleAddCustomer = (data: TCustomerFormData) => {
    setCustomerDetails(data);
  };

  const handleOpenSelectProductModal = () => {
    showModal({
      body: <SelectProductModal onSubmit={handleAddProduct} initialValue={selectedProducts} />,
      width: 640,
      modalProps: { showCloseIcon: false }
    });
  };

  const handleOpenAddCustomerDetailsModal = () => {
    showModal({
      body: <AddressModal onSubmit={handleAddCustomer} initialValue={customerDetails} />,
      width: 640,
      modalProps: { showCloseIcon: false }
    });
  };

  return (
    <CustomCardContent className="flex flex-col gap-4">
      {/** Header */}
      <div className="flex items-center justify-between pb-4 border-b border-b-v2-border-secondary">
        <div className="text-v2-content-primary text-base font-semibold">{t("order.manualOrder")}</div>
        <Button
          variant="secondaryGray"
          startAdornment={
            <Icon icon="hugeicons:edit-02" className="text-v2-content-secondary !size-5 shrink-0 cursor-pointer" />
          }
          onClick={handleOpenSelectProductModal}
        >
          {t("order.manualOrderEdit")}
        </Button>
      </div>

      <div className="">
        <Input
          startAdornment={
            <div className="border-l pl-1.5 cursor-pointer">
              <Icon icon="solar:magnifer-linear" width="1.1rem" color="#ADADAD" height="1.1rem" className="" />
            </div>
          }
          variant="filled"
          placeholder={t("order.manualOrderInputPlaceHolder")}
          readOnly
          className="cursor-pointer"
          onClick={handleOpenSelectProductModal}
        />
      </div>

      <div className="flex flex-col gap-2">
        {/** Header */}
        <div className="flex items-center justify-between border-b border-v2-border-secondary pb-2">
          <div className="gap-2 flex items-center w-4/6">
            <Image src="/images/icons/cart3.svg" alt="editAddress" width={20} height={20} />

            <div className="text-v2-content-primary font-semibold text-[15px]">
              {t("order.manualOrderOrderedItems")}
            </div>
          </div>
          <div className="w-1/6 text-v2-content-tertiary text-[15px] font-semibold">
            {t("order.manualOrderQuantity")}
          </div>
          <div className="w-1/6 text-v2-content-tertiary text-[15px] font-semibold">{t("order.manualOrderSum")}</div>
        </div>

        {/** Table */}
        <div className="flex flex-col divide-y divide-v2-border-secondary">
          {selectedProducts?.length ? (
            selectedProducts.map((item, index) => (
              <div key={`${item.id}-${index}`} className="flex items-center justify-between py-3 last:pb-0 first:pt-1">
                <div className="flex items-center gap-3 w-4/6">
                  <div className="size-[70px] bg-foreground-neutral-secondary/50 rounded-lg overflow-hidden relative">
                    <Image
                      src={item.product.cover?.url || "/images/placeholder.jpg"}
                      alt={item.product.title}
                      fill
                      className="object-cover"
                    />
                  </div>
                  <div className="flex flex-col gap-3">
                    <div className="font-medium text-sm text-v2-content-primary">{item.product.title}</div>
                    <div className="flex items-center gap-3">
                      {item.variant && (
                        <div className="text-[13px] rounded-[5px] px-[5px] py-0.5 bg-v2-surface-thertiary text-v2-content-secondary font-medium w-fit">
                          {item.variant.options
                            ? Object.entries(item.variant.options)
                                .map(([key, value]) => `${key}: ${value}`)
                                .join(" | ")
                            : "Default variant"}
                        </div>
                      )}

                      <div className="font-semibold text-v2-content-on-info text-sm">
                        {renderPrice(item.variant?.retailPrice || item.product.cheapestPrice)}
                      </div>
                    </div>
                  </div>
                </div>

                <div className="w-1/6">
                  <div className="flex items-center gap-1 border-2 border-v2-border-secondary rounded-md px-2 py-1.5 w-fit">
                    <button
                      className="text-v2-content-primary rounded-md"
                      onClick={() =>
                        setSelectedProducts(prev =>
                          prev.map(p => (p.id === item.id ? { ...p, quantity: p.quantity + 1 } : p))
                        )
                      }
                    >
                      <Icon icon="tabler:plus" className="size-5" />
                    </button>

                    <span className="w-8 text-center font-medium text-sm">{item.quantity}</span>

                    <button
                      className="text-v2-content-primary rounded-md"
                      onClick={() => {
                        if (item.quantity === 1) {
                          setSelectedProducts(prev => prev.filter(p => p.id !== item.id));
                        } else {
                          setSelectedProducts(prev =>
                            prev.map(p => (p.id === item.id ? { ...p, quantity: p.quantity - 1 } : p))
                          );
                        }
                      }}
                    >
                      <Icon
                        icon={item.quantity === 1 ? "solar:trash-bin-trash-outline" : "tabler:minus"}
                        className="size-5"
                      />
                    </button>
                  </div>
                </div>

                <div className="w-1/6 text-right font-medium text-v2-content-primary text-sm">
                  {renderPrice((item.variant?.retailPrice || item.product.cheapestPrice) * item.quantity)}
                </div>
              </div>
            ))
          ) : (
            <div className="flex flex-col items-center py-8 gap-2">
              <div className="text-center text-v2-content-tertiary">{t("order.noProductsSelected")}</div>

              <Button
                variant="secondaryColor"
                startAdornment={
                  customerDetails ? (
                    <Icon
                      icon="hugeicons:edit-02"
                      className="text-v2-content-secondary !size-5 shrink-0 cursor-pointer"
                    />
                  ) : (
                    <IconPlus className="size-5" />
                  )
                }
                className="w-fit"
                onClick={handleOpenSelectProductModal}
              >
                {t("order.manualOrderAddProduct")}
              </Button>
            </div>
          )}
        </div>
      </div>

      <div className="border-2 rounded-lg border-v2-border-secondary p-4 flex flex-col gap-4">
        <div className="gap-2 flex items-center w-4/6">
          <Image src="/images/icons/cart3.svg" alt="editAddress" width={20} height={20} />

          <div className="text-v2-content-primary font-semibold text-[15px]">{t("order.manualOrderCustomerInfo")}</div>
        </div>
        {customerDetails && (
          <div className="flex flex-col gap-2">
            <div className="text-v2-content-primary text-sm font-semibold">
              {customerDetails?.firstName} {customerDetails?.lastName}
            </div>

            <div className="text-v2-content-primary text-sm font-medium">{customerDetails?.address}</div>

            <div className="flex flex-col gap-1">
              <div className="font-semibold text-sm text-v2-content-primary">
                <span className="text-v2-content-tertiary font-medium">{t("order.manualOrderMobile")}</span>{" "}
                {customerDetails?.mobile}
              </div>
              <div className="font-semibold text-sm text-v2-content-primary">
                <span className="text-v2-content-tertiary font-medium">{t("order.manualOrderPostalCode")}</span>{" "}
                {customerDetails?.postalCode}
              </div>
            </div>
          </div>
        )}
        <Button
          variant="secondaryColor"
          startAdornment={
            customerDetails ? (
              <Icon icon="hugeicons:edit-02" className="text-v2-content-secondary !size-5 shrink-0 cursor-pointer" />
            ) : (
              <IconPlus className="size-5" />
            )
          }
          className="w-fit"
          onClick={handleOpenAddCustomerDetailsModal}
        >
          {customerDetails ? t("order.manualOrderEditCustomer") : t("order.manualOrderAddCustomer")}
        </Button>
      </div>

      {/** Footer */}
      <div className="flex items-center justify-end gap-4">
        <Button variant="secondaryGray" onClick={() => router.back()}>
          {t("order.manualOrderCancel")}
        </Button>
        <Button variant="secondaryColor">{t("order.manualOrderSubmitOrder")}</Button>
      </div>
    </CustomCardContent>
  );
}

import Input from "@/components/ui/inputs/Input";
import { useGetProductListQuery } from "@/store/apps/product";
import useModal from "@/utils/hooks/useModal";
import { Icon } from "@iconify/react";
import { Close } from "@mui/icons-material";
import { CircularProgress, IconButton } from "@mui/material";
import Image from "next/image";
import { useState, useMemo } from "react";
import { useTranslation } from "react-i18next";
import EmptyPage from "../RetailerProduct/EmptyPage";
import { generateBackendFilters, generateBackendSorts } from "@/utils/services/transformers";
import useDebouncedInput from "@/utils/hooks/useDebouncedInput";
import CustomCheckbox from "@/components/ui/CustomCheckbox/CustomCheckbox";
import { twMerge } from "tailwind-merge";
import useCurrency from "@/utils/hooks/useCurrency";
import Button from "@/components/ui/Button";
import CustomAutocomplete from "@/components/ui/CustomAutocomplete/CustomAutocomplete";
import { omitEmptyValues } from "@/utils/helpers";
import { ListInfiniteScroll } from "@/components/ui/ListInfiniteScroll";
import { IProductPayLoad, IVariantPayload } from "@/store/apps/product/types";

export interface SelectedProductItem {
  id: string;
  product: IProductPayLoad;
  variant: IVariantPayload;
  quantity: number;
}

export default function SelectProductModal({
  onSubmit,
  initialValue
}: {
  onSubmit?: (selectedItems: SelectedProductItem[]) => void;
  initialValue?: SelectedProductItem[];
}) {
  const { t } = useTranslation();
  const { hideModal } = useModal();
  const { value, setValue, debouncedValue } = useDebouncedInput();
  const [createdAtFilter, setCreatedAtFilter] = useState("desc");
  const [selectedItems, setSelectedItems] = useState<SelectedProductItem[]>(initialValue || []);
  const [page, setPage] = useState(1);
  const [allProducts, setAllProducts] = useState<IProductPayLoad[]>([]);
  const [curr] = useCurrency();
  const { render: renderPrice } = curr ?? { render: v => v };

  const pageSize = 20; // Define page size

  const finalFilters = generateBackendFilters({ title: debouncedValue });

  const sorts = generateBackendSorts(
    omitEmptyValues({
      created_at: (createdAtFilter as any) || undefined
    })
  );

  const queryString = [
    pageSize ? `page_size=${pageSize}` : "",
    page ? `page=${page}` : "",
    finalFilters ? `filters=${finalFilters}` : "",
    sorts ? `sorts=${sorts}` : ""
  ]
    ?.filter(part => part !== "")
    ?.join("&");

  const {
    data: productData,
    isLoading: isProductLoading,
    isFetching: isProductFetching,
    isError: isProductError
  } = useGetProductListQuery(queryString, {
    refetchOnMountOrArgChange: true,
    skip: false
  });

  // Reset products and page when search or filter changes
  useMemo(() => {
    setPage(1);
    setAllProducts([]);
  }, [debouncedValue, createdAtFilter]);

  // Accumulate products when new data arrives
  useMemo(() => {
    if (productData?.data) {
      if (page === 1) {
        // First page - replace all products
        setAllProducts(productData.data);
      } else {
        // Subsequent pages - append to existing products
        setAllProducts(prev => {
          const existingIds = new Set(prev.map(p => p.id));
          const newProducts = productData.data.filter(p => !existingIds.has(p.id));
          return [...prev, ...newProducts];
        });
      }
    }
  }, [productData, page]);

  // Calculate if there are more pages
  const hasNextPage = useMemo(() => {
    if (!productData?.pagination) return false;
    const { page: currentPage, pageSize, total } = productData.pagination;
    return allProducts.length < total;
  }, [productData?.pagination, allProducts.length]);

  // Function to fetch next page
  const fetchNextPage = () => {
    if (!isProductFetching && hasNextPage) {
      setPage(prev => prev + 1);
    }
  };

  // Toggle single item selection
  const toggleItem = (variantId: string, product: IProductPayLoad, variant: IVariantPayload) => {
    setSelectedItems(prevSelected => {
      const exists = prevSelected.find(item => item.id === variantId);
      if (exists) {
        return prevSelected.filter(item => item.id !== variantId);
      } else {
        return [
          ...prevSelected,
          {
            id: variantId,
            product: product,
            variant: variant,
            quantity: 1
          }
        ];
      }
    });
  };

  // Toggle all variants of a product
  const toggleAllVariants = (product: IProductPayLoad) => {
    const variantIds = product?.variants?.map(v => v.id || "").filter(Boolean) || [];

    setSelectedItems(prevSelected => {
      const selectedVariantIds = prevSelected.filter(item => item.product.id === product.id).map(item => item.id);

      const allSelected = variantIds.every(id => selectedVariantIds.includes(id));

      if (allSelected) {
        // Remove all variants for this product
        return prevSelected.filter(item => !variantIds.includes(item.id));
      } else {
        // Add all missing variants for this product
        const existingVariantIds = new Set(selectedVariantIds);
        const newVariants = product.variants?.filter(v => v.id && !existingVariantIds.has(v.id)) || [];

        const newItems = newVariants.map(variant => ({
          id: variant.id!,
          product: product,
          variant: variant,
          quantity: 1
        }));

        return [...prevSelected.filter(item => !variantIds.includes(item.id)), ...newItems];
      }
    });
  };

  const handleSubmit = () => {
    onSubmit?.(selectedItems);
    hideModal();
  };

  return (
    <div className="flex flex-col gap-6">
      {/** Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <div className="rounded-full w-auto h-auto p-1 bg-v2-surface-action-light">
            <Image src="/images/icons/cart-plus.svg" width={15} height={15} alt="" />
          </div>
          <div className="text-[13px] font-medium">{t("order.manualOrderSelectProduct")}</div>
        </div>

        <IconButton className="block p-0 size-5">
          <Close className="size-5" onClick={hideModal} />
        </IconButton>
      </div>

      {/** Search bar */}
      <div className="flex gap-4 items-center w-full">
        <Input
          placeholder={t("order.manualOrderSearchProduct")}
          rootClassName="flex-1"
          value={value}
          onChange={e => setValue(e.target?.value)}
        />
        <div>
          <CustomAutocomplete
            readOnlyInput
            placeholder={t("order.manualOrderOrderProduct")}
            startAdornment={<Icon icon="material-symbols:sort-rounded" />}
            className="text-v2-content-primary placeholder:text-v2-content-primary"
            options={[
              { id: "asc", label: t("retailerProduct.filters.oldest") },
              { id: "desc", label: t("retailerProduct.filters.newest") }
            ]}
            value={{
              id: createdAtFilter,
              label:
                createdAtFilter === "asc" ? t("retailerProduct.filters.oldest") : t("retailerProduct.filters.newest")
            }}
            onChange={(e, value) => setCreatedAtFilter(value?.id as string)}
          />
        </div>
      </div>

      <div className="overflow-y-auto max-h-[40vh]">
        {isProductLoading && page === 1 && !isProductError && (
          <div className="py-40 flex items-center justify-center">
            <CircularProgress />
          </div>
        )}

        {!isProductLoading && (!allProducts?.length || isProductError) && (
          <div className="py-8 flex items-center justify-center">
            <EmptyPage imageWidth={100} />
          </div>
        )}

        {allProducts?.length > 0 && (
          <div className="divide-y divide-v2-border-secondary">
            {allProducts.map(product => {
              // Get all variant IDs for this product
              const variantIds: string[] = product?.variants?.map(v => v.id || "").filter(Boolean) || [];

              // Check if all variants are selected
              const selectedVariantIds = selectedItems
                .filter(item => item.product.id === product.id)
                .map(item => item.id);
              const allVariantsSelected =
                variantIds.length > 0 && variantIds.every(id => selectedVariantIds.includes(id));

              // Check if some (but not all) variants are selected
              const someVariantsSelected =
                variantIds.some(id => selectedVariantIds.includes(id)) && !allVariantsSelected;

              return (
                <div key={product?.id} className="flex flex-col divide-y divide-v2-border-secondary">
                  <div className={twMerge("flex items-center gap-3 py-2", product?.variants?.length > 1 ? "" : "")}>
                    <div className="shrink-0">
                      <CustomCheckbox
                        className="!p-0 !ml-0 !mr-0 [&>span]:!p-0"
                        checked={allVariantsSelected}
                        indeterminate={someVariantsSelected}
                        onChange={() => {
                          if (product?.variants?.length === 1) {
                            // Single variant case
                            const variant = product.variants[0];
                            if (variant?.id) {
                              toggleItem(variant.id, product, variant);
                            }
                          } else if (variantIds?.length > 1) {
                            // Multiple variants case
                            toggleAllVariants(product);
                          }
                        }}
                      />
                    </div>
                    <div className="overflow-hidden size-[46px] rounded-md border border-v2-border-secondary shrink-0">
                      <Image src={product?.cover?.url} width={46} height={46} alt={product?.title} />
                    </div>
                    <div className="text-sm font-medium text-v2-content-primary flex-1">{product?.title}</div>

                    {(!product?.variants || product?.variants?.length <= 1) && (
                      <div className="text-sm font-medium text-v2-content-primary">
                        {renderPrice(product?.cheapestPrice)}
                      </div>
                    )}
                  </div>

                  {product?.variants &&
                    product?.variants?.length > 1 &&
                    product?.variants?.map(variant => {
                      const isChecked = selectedItems.some(item => item.id === (variant?.id || ""));

                      return (
                        <div key={variant?.id} className="py-2.5 pr-8 flex items-center gap-2">
                          <div className="shrink-0">
                            <CustomCheckbox
                              className="!p-0 !ml-0 !mr-0 [&>span]:!p-0"
                              checked={isChecked}
                              onChange={() => {
                                if (variant?.id) {
                                  toggleItem(variant.id, product, variant);
                                }
                              }}
                            />
                          </div>

                          <div className="text-sm font-medium text-v2-content-primary flex-1">
                            {variant?.options
                              ? Object.entries(variant.options)
                                  .map(item => `${item?.[0]}: ${item?.[1]}`)
                                  .join(" | ")
                              : "-"}
                          </div>
                          <div className="text-sm font-medium text-v2-content-primary">
                            {renderPrice(variant?.retailPrice)}
                          </div>
                        </div>
                      );
                    })}
                </div>
              );
            })}

            <ListInfiniteScroll hasNextPage={hasNextPage} fetchNextPage={fetchNextPage} />
          </div>
        )}
      </div>

      {/** Footer */}
      <div className="flex items-center justify-between gap-4">
        <div className="flex-1">
          {selectedItems?.length > 0 && (
            <div className="w-full text-base text-v2-content-primary font-medium">
              {selectedItems?.length} {t("order.manualOrderSelectedCount")}
            </div>
          )}
        </div>
        <div className="flex items-center gap-4">
          <Button variant="secondaryGray" onClick={hideModal}>
            {t("order.manualOrderCancel")}
          </Button>
          <Button variant="primary" onClick={handleSubmit}>
            {t("order.manualOrderSubmit")}
          </Button>
        </div>
      </div>
    </div>
  );
}

import Input from "@/components/ui/inputs/Input";
import useModal from "@/utils/hooks/useModal";
import { Close } from "@mui/icons-material";
import { IconButton } from "@mui/material";
import Image from "next/image";
import { useTranslation } from "react-i18next";
import { Controller, useForm } from "react-hook-form";
import { yupResolver } from "@hookform/resolvers/yup";
import * as yup from "yup";
import i18n from "@/utils/i18n";
import Button from "@/components/ui/Button";
import Textarea from "@/components/ui/inputs/Textarea/Textarea";
import LocationsSelect from "@/components/ui/CustomCountrySelect/LocationsSelect";

export const validationSchema = yup.object({
  firstName: yup
    .string()
    .required(i18n.t("product.validations.required"))
    .max(120, i18n.t("product.validations.maxChar", { amount: 120 })),
  lastName: yup
    .string()
    .required(i18n.t("product.validations.required"))
    .max(120, i18n.t("product.validations.maxChar", { amount: 120 })),
  locationId: yup.string().required(i18n.t("product.validations.required")),
  address: yup.string().required(i18n.t("product.validations.required")),
  mobile: yup.string().required(i18n.t("product.validations.required")),
  postalCode: yup.string().required(i18n.t("product.validations.required"))
});

export type TCustomerFormData = yup.InferType<typeof validationSchema>;

export default function AddressModal({
  onSubmit,
  initialValue
}: {
  onSubmit?: (formData: TCustomerFormData) => void;
  initialValue?: TCustomerFormData;
}) {
  const { t } = useTranslation();
  const { hideModal } = useModal();

  const handleSubmit = (vals: TCustomerFormData) => {
    onSubmit?.(vals);
    hideModal();
  };

  const form = useForm<TCustomerFormData>({
    defaultValues: initialValue,
    resolver: yupResolver(validationSchema as any) as any
  });

  const {
    formState: { errors },
    control
  } = form;

  return (
    <form onSubmit={form?.handleSubmit(handleSubmit)} className="h-full flex flex-col gap-6">
      {/** Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <div className="rounded-full w-auto h-auto p-1 bg-v2-surface-action-light">
            <Image src="/images/icons/signpost2.svg" width={15} height={15} alt="" />
          </div>
          <div className="text-[13px] font-medium">
            {initialValue ? t("order.manualOrderEditAddress") : t("order.manualOrderAddAddress")}
          </div>
        </div>

        <IconButton className="block p-0 size-5">
          <Close className="size-5" onClick={hideModal} />
        </IconButton>
      </div>

      <div className="grid grid-cols-2 gap-4 w-full items-start">
        <Controller
          name="firstName"
          control={control}
          render={({ field, fieldState: { error } }) => (
            <Input
              {...field}
              placeholder={t("order.manualOrderFirstName")}
              error={Boolean(errors.firstName?.message)}
              helperText={errors?.firstName?.message || ""}
            />
          )}
        />
        <Controller
          name="lastName"
          control={control}
          render={({ field, fieldState: { error } }) => (
            <Input
              {...field}
              placeholder={t("order.manualOrderLastName")}
              error={Boolean(errors.lastName?.message)}
              helperText={errors?.lastName?.message || ""}
            />
          )}
        />
        <Controller
          name="locationId"
          control={control}
          render={({ field }) => (
            <LocationsSelect
              multiple={false}
              label={t("statecity")}
              {...field}
              error={Boolean(errors?.locationId?.message)}
              helperText={errors?.locationId?.message || ""}
              requiredStar={false}
            />
          )}
        />
        <div className="col-span-2 ">
          <Controller
            name="address"
            control={control}
            render={({ field }) => (
              <Textarea
                {...field}
                autoComplete="off"
                rows={5}
                // optional
                label={t("order.manualOrderFullAddress")}
                placeholder={t("order.manualOrderAddress")}
                // maxCharLength={4098}
                requiredStar={false}
                error={Boolean(errors.address?.message)}
                helperText={errors?.address?.message || ""}
              />
            )}
          />
        </div>
        <Controller
          name="mobile"
          control={control}
          render={({ field, fieldState: { error } }) => (
            <Input
              {...field}
              label={t("order.manualOrderMobile")}
              placeholder={t("order.manualOrderMobilePlaceholder")}
              error={Boolean(errors.mobile?.message)}
              helperText={errors?.mobile?.message || ""}
              requiredStar={false}
            />
          )}
        />
        <Controller
          name="postalCode"
          control={control}
          render={({ field, fieldState: { error } }) => (
            <Input
              {...field}
              label={t("order.manualOrderPostalCode")}
              placeholder={t("order.manualOrderPostalCode")}
              error={Boolean(errors.postalCode?.message)}
              helperText={errors?.postalCode?.message || ""}
              requiredStar={false}
            />
          )}
        />
      </div>

      {/** Footer */}
      <div className="flex items-center gap-4">
        <Button variant="secondaryGray" onClick={hideModal} type="button" className="flex-1">
          {t("order.manualOrderCancel")}
        </Button>
        <Button variant="primary" type="submit" className="flex-1">
          {t("order.manualOrderSubmit")}
        </Button>
      </div>
    </form>
  );
}

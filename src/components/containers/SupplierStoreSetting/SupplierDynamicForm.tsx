/* eslint-disable @typescript-eslint/no-unused-vars */
import React, { useEffect } from "react";
import { FormikHelpers } from "formik";
import { FormElementData, IntegrationData } from "@/store/apps/meta/types";
import { Box, CircularProgress } from "@mui/material";
import { routes } from "@/constants/routes";
import { useTranslation } from "react-i18next";
import useRoleBasePath from "@/utils/hooks/useRoleBasePath";
import useCurrency from "@/utils/hooks/useCurrency";
import { useParams, usePathname, useRouter } from "next/navigation";
import { clientDefaultErrorHandler, SetHookFormError } from "@/utils/services/utils";
import { IntegrationsPutDataResponse } from "@/store/apps/retailer/types";
import { ensureUrlScheme, snakeToCamelCaseHookFormWrapper } from "@/utils/helpers";
import { useDispatch } from "@/store/hooks";
import { SUPPLIER_INTEGRATION_KEY } from "@/constants/queryKeys";
import DynamicForm from "@/components/containers/DynamicForm/DynamicForm";
import { Supplier, usePostSupplierStoreMutation, usePutSupplierStoreMutation } from "@/store/apps/supplier";
import { TSupplierIntegrationResponse } from "@/store/apps/supplier/types";
import { Icon } from "@iconify/react";
import useModal from "@/utils/hooks/useModal";

interface ISupplierDynamicFormProps {
  id?: string;
  configForm: { [key: string]: FormElementData };
  isLoading: boolean;
  isCreate?: boolean;
  supplierStore?: TSupplierIntegrationResponse;
  isStoreSingleLoading?: boolean;
  setIsConnecting?: (value: boolean) => void;
  handleSubmit?: () => void;
  setIsLoading?: (val: boolean) => void;
  integration?: IntegrationData;
}

const SupplierDynamicForm = ({
  isCreate,
  configForm,
  isLoading,
  id,
  handleSubmit,
  supplierStore,
  setIsConnecting,
  setIsLoading,
  integration,
  isStoreSingleLoading
}: ISupplierDynamicFormProps) => {
  const router = useRouter();
  const pathname = usePathname();
  const { hideModal } = useModal();
  const makePath = useRoleBasePath();
  const params = useParams();
  const [_selected, _selectCurrency] = useCurrency();
  const dispatch = useDispatch();
  const { t } = useTranslation();

  const [postSupplierStore, { isLoading: isPostSupplierStoreLoading }] = usePostSupplierStoreMutation();
  const [putSupplierStore, { isLoading: isPutSupplierStoreLoading }] = usePutSupplierStoreMutation();

  const isSubmitting = isPostSupplierStoreLoading || isPutSupplierStoreLoading;

  const initialValues = Object.keys(configForm).reduce(
    (acc, key) => {
      acc[key] = supplierStore?.data?.config?.[key] || "";
      return acc;
    },
    {} as { [key: string]: any }
  );

  const onSubmit = async (
    { preferences, adjustmentPercentage, wholesalePrice, ...values }: { [key: string]: any },
    { setFieldError }: FormikHelpers<typeof initialValues>
  ) => {
    const body = {
      config: values,
      preferences,
      platform: id || supplierStore?.data?.integration?.platform?.key,
      adjustmentPercentage,
      wholesalePrice,
      isActive: true,
      returnUri: `${pathname}?settingType=store&storeStep=2&isConnected=true`
    } as any;

    setIsLoading?.(true);

    try {
      const supplierStoreApi = postSupplierStore({ body });

      await supplierStoreApi.then(res => {
        const error = (res as any)?.error?.data;

        const resData = (res as any)?.data?.data as IntegrationsPutDataResponse["data"];

        if (error) {
          if (error.error_detail.handle?.includes("already exists")) {
            const bodyError = {
              ...error,
              error_detail: { handle: ["userDuplicate"] }
            };

            clientDefaultErrorHandler({
              error: (res as any)?.error,
              bodyError,
              setFieldError
            });
          } else
            clientDefaultErrorHandler({
              bodyError: error,
              error: (res as any)?.error,
              setFieldError
            });
        } else if ("data" in res && res?.data) {
          if (!isCreate) {
            hideModal();
          } else setIsConnecting?.(true);
          if (resData?.shouldRedirect && resData?.redirectTo && isCreate) {
            const width = Math.round(window.innerWidth * 0.9);
            const height = Math.round(window.innerHeight * 0.9);

            router.replace(`${pathname}?settingType=store&storeStep=2`);

            setTimeout(() => {
              // window.open(ensureUrlScheme(resData?.redirectTo ?? ""), "popup", `width=${width},height=${height}`);
              window.location.href = ensureUrlScheme(resData?.redirectTo ?? "");
              // window.open(ensureUrlScheme(resData?.redirectTo ?? ""), "_blank");
            }, 100);
          } else router.replace(`${pathname}?settingType=store&storeStep=2`);

          setTimeout(() => {
            setIsLoading?.(false);
          }, 3000);

          handleSubmit?.();

          dispatch(Supplier.util.invalidateTags([{ type: SUPPLIER_INTEGRATION_KEY }]));
        }
      });
    } catch (err: any) {
      clientDefaultErrorHandler({ error: err });
      setIsLoading?.(false);
    }
  };

  const onCancel = () => {
    router.push(makePath(routes.product));
    // hideModal();
  };

  const isAuth = integration?.supplierOauth;

  const getSubmitButtonText = (): string => {
    // if (isEdit) return t("saveChanges");
    if (isAuth) return t("authentication");
    return t("confirm&continue");
  };

  const submitButtonText = getSubmitButtonText();

  if (isLoading || isStoreSingleLoading) {
    return (
      <Box className="flex items-center justify-center">
        <CircularProgress />
      </Box>
    );
  }

  return (
    <div className={"mt-0"}>
      <DynamicForm
        gridColumn={12}
        isLoading={isLoading}
        configForm={configForm}
        handleSubmit={onSubmit}
        isSubmitting={isSubmitting}
        storeDataSingle={supplierStore?.data}
        isStoreSingleLoading={isStoreSingleLoading}
        submitButtonText={submitButtonText}
        cancelButtonText={t("cancelButton")}
        onCancel={onCancel}
        // renderBottom={renderBottom}
        actionContainerClassName="absolute -bottom-16 right-0 w-full !pr-0"
      />
    </div>
  );
};

export default SupplierDynamicForm;

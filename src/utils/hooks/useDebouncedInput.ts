import { debounce } from "lodash";
import { useCallback, useState, useEffect, useRef } from "react";

export default function useDebouncedInput<T = string>({
  initialValue = undefined,
  debounceTime = 2000
}: { initialValue?: T; debounceTime?: number } = {}) {
  const [value, setValue] = useState<T | undefined>(initialValue);
  const [debouncedValue, setDebouncedValue] = useState<T | undefined>(initialValue);

  // Create a ref to store the debounced function so it persists across renders
  const debouncedSetValue = useRef(
    debounce((newValue: T) => {
      setDebouncedValue(newValue);
    }, debounceTime)
  );

  // Update the debounced function when debounceTime changes
  useEffect(() => {
    debouncedSetValue.current = debounce((newValue: T) => {
      setDebouncedValue(newValue);
    }, debounceTime);
  }, [debounceTime]);

  // Cleanup the debounced function on unmount
  useEffect(() => {
    return () => {
      debouncedSetValue.current.cancel();
    };
  }, []);

  const handleOnChange = useCallback((newValue: T) => {
    setValue(newValue);
    debouncedSetValue.current(newValue);
  }, []);

  return {
    value,
    debouncedValue,
    setValue: handleOnChange
  };
}
